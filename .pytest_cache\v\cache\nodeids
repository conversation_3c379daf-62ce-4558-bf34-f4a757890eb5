["test_scraper.py::TestRedditScraper::test_advanced_media_downloader_config", "test_scraper.py::TestRedditScraper::test_argument_validation", "test_scraper.py::TestRedditScraper::test_bundled_mode", "test_scraper.py::TestRedditScraper::test_config_validation", "test_scraper.py::TestRedditScraper::test_configuration_loading", "test_scraper.py::TestRedditScraper::test_content_filtering", "test_scraper.py::TestRedditScraper::test_data_storage_csv", "test_scraper.py::TestRedditScraper::test_data_storage_json", "test_scraper.py::TestRedditScraper::test_directory_creation", "test_scraper.py::TestRedditScraper::test_external_url_processing", "test_scraper.py::TestRedditScraper::test_file_extension_detection", "test_scraper.py::TestRedditScraper::test_gif_image_type_filtering_fix", "test_scraper.py::TestRedditScraper::test_media_downloader_downloadable_check", "test_scraper.py::TestRedditScraper::test_media_downloader_url_processing", "test_scraper.py::TestRedditScraper::test_media_only_mode", "test_scraper.py::TestRedditScraper::test_media_type_detection", "test_scraper.py::TestRedditScraper::test_placeholder_detection", "test_scraper.py::TestRedditScraper::test_quality_detection", "test_scraper.py::TestRedditScraper::test_reddit_gallery_gif_handling", "test_scraper.py::TestRedditScraper::test_reddit_gallery_handling", "test_scraper.py::TestRedditScraper::test_resolution_parsing", "test_scraper.py::TestRedditScraper::test_sanitize_filename", "test_scraper.py::TestRedditScraper::test_summary_report_generation", "test_scraper.py::TestRedditScraper::test_url_prioritization"]