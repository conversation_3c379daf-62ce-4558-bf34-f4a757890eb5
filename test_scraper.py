#!/usr/bin/env python3
"""
Test script for Reddit Scraper
Tests various components and functionality
"""

import unittest
import tempfile
import shutil
from pathlib import Path
import json
import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from reddit_scraper import RedditScraper, RedditPost
from media_downloader import MediaDownloader
from data_storage import DataStorage
from config import Config


class TestRedditScraper(unittest.TestCase):
    """Test cases for Reddit Scraper functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.scraper = RedditScraper(rate_limit_delay=0.1)  # Faster for testing
        self.storage = DataStorage(base_dir=self.temp_dir)
        self.downloader = MediaDownloader(base_download_dir=self.temp_dir, rate_limit_delay=0.1)
        
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
    def test_sanitize_filename(self):
        """Test filename sanitization"""
        from media_downloader import MediaDownloader
        downloader = MediaDownloader()

        test_cases = [
            ("normal_filename.txt", "normal_filename.txt"),
            ("file with spaces.txt", "file_with_spaces.txt"),
            ("file<>:\"/\\|?*.txt", "file_________.txt"),
            ("very_long_filename_" + "x" * 200 + ".txt", "very_long_filename_" + "x" * 177 + ".txt"),
            ("", "unnamed"),
            ("...", "unnamed")
        ]

        for input_name, expected in test_cases:
            with self.subTest(input_name=input_name):
                result = downloader.sanitize_filename(input_name)
                self.assertEqual(result, expected)
                
    def test_config_validation(self):
        """Test configuration validation"""
        config = Config()
        self.assertTrue(config.validate_settings())
        
        # Test invalid settings
        config.set('rate_limit_delay', -1)
        self.assertFalse(config.validate_settings())
        
    def test_data_storage_json(self):
        """Test JSON data storage"""
        # Create sample posts
        posts = [
            RedditPost(
                title="Test Post 1",
                author="test_user",
                score=100,
                comments_count=50,
                created_utc="2024-01-01T00:00:00Z",
                post_type="text",
                url="https://reddit.com/test1",
                permalink="https://reddit.com/test1",
                content="Test content",
                media_urls=[],
                subreddit="test",
                post_id="test1"
            ),
            RedditPost(
                title="Test Post 2",
                author="test_user2",
                score=200,
                comments_count=75,
                created_utc="2024-01-01T01:00:00Z",
                post_type="image",
                url="https://i.redd.it/test.jpg",
                permalink="https://reddit.com/test2",
                content="",
                media_urls=["https://i.redd.it/test.jpg"],
                subreddit="test",
                post_id="test2"
            )
        ]
        
        # Save to JSON
        json_path = self.storage.save_posts_json(posts, "test", "test_posts.json")
        self.assertTrue(json_path.exists())
        
        # Load and verify
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        self.assertIn('metadata', data)
        self.assertIn('posts', data)
        self.assertEqual(len(data['posts']), 2)
        self.assertEqual(data['metadata']['total_posts'], 2)
        self.assertEqual(data['metadata']['subreddit'], "test")
        
    def test_data_storage_csv(self):
        """Test CSV data storage"""
        posts = [
            {
                'title': 'Test Post',
                'author': 'test_user',
                'score': 100,
                'comments_count': 50,
                'post_type': 'text',
                'media_urls': ['url1', 'url2']
            }
        ]
        
        csv_path = self.storage.save_posts_csv(posts, "test", "test_posts.csv")
        self.assertTrue(csv_path.exists())
        
        # Read and verify CSV
        import csv
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            rows = list(reader)
            
        self.assertEqual(len(rows), 1)
        self.assertEqual(rows[0]['title'], 'Test Post')
        self.assertEqual(rows[0]['media_urls'], 'url1; url2')  # Should be joined
        
    def test_media_downloader_url_processing(self):
        """Test media URL processing"""
        test_cases = [
            ("https://imgur.com/abc123", ["https://i.imgur.com/abc123.jpg"]),
            ("https://i.imgur.com/abc123.jpg", ["https://i.imgur.com/abc123.jpg"]),
            ("https://imgur.com/a/gallery123", ["https://imgur.com/a/gallery123"]),
            ("https://i.redd.it/test.jpg", ["https://i.redd.it/test.jpg"])
        ]

        for input_url, expected in test_cases:
            with self.subTest(input_url=input_url):
                result = self.downloader.process_imgur_url(input_url)
                self.assertEqual(result, expected)
                
    def test_media_downloader_downloadable_check(self):
        """Test downloadable URL detection"""
        downloadable_urls = [
            "https://i.redd.it/test.jpg",
            "https://i.imgur.com/test.png",
            "https://v.redd.it/test.mp4",
            "https://example.com/image.gif"
        ]
        
        non_downloadable_urls = [
            "https://youtube.com/watch?v=123",
            "https://twitter.com/status/123",
            "https://example.com/page.html"
        ]
        
        for url in downloadable_urls:
            with self.subTest(url=url):
                self.assertTrue(self.downloader.is_downloadable_url(url))
                
        for url in non_downloadable_urls:
            with self.subTest(url=url):
                self.assertFalse(self.downloader.is_downloadable_url(url))
                
    def test_directory_creation(self):
        """Test directory structure creation"""
        subreddit_dir = self.storage.create_subreddit_directory("testsubreddit")

        self.assertTrue(subreddit_dir.exists())
        # Note: subdirectories are created on-demand, not upfront
        self.assertTrue(subreddit_dir.is_dir())
        
    def test_summary_report_generation(self):
        """Test summary report generation"""
        posts = [
            {
                'title': 'Text Post',
                'author': 'user1',
                'score': 100,
                'comments_count': 50,
                'post_type': 'text',
                'media_urls': []
            },
            {
                'title': 'Image Post',
                'author': 'user2',
                'score': 200,
                'comments_count': 75,
                'post_type': 'image',
                'media_urls': ['https://i.redd.it/test.jpg']
            }
        ]
        
        media_stats = {
            'total_files': 5,
            'total_size': 1024 * 1024,  # 1MB
            'post_count': 2,
            'images': 3,
            'gifs': 1,
            'videos': 1,
            'other': 0
        }
        
        report_path = self.storage.create_summary_report(posts, "test", media_stats)
        self.assertTrue(report_path.exists())
        
        # Read and verify report content
        with open(report_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        self.assertIn("Total posts scraped: 2", content)
        self.assertIn("Unique authors: 2", content)
        self.assertIn("Posts with media: 1", content)
        self.assertIn("Total files downloaded: 5", content)
        self.assertIn("text: 1 (50.0%)", content)
        self.assertIn("image: 1 (50.0%)", content)
        self.assertIn("MEDIA TYPE BREAKDOWN:", content)
        self.assertIn("Images: 3", content)
        self.assertIn("GIFs: 1", content)
        self.assertIn("Videos: 1", content)

    def test_media_type_detection(self):
        """Test media type detection functionality"""
        from media_downloader import MediaDownloader
        downloader = MediaDownloader()

        # Test various media type detections
        test_cases = [
            ('https://i.redd.it/test.jpg', 'image/jpeg', 'image'),
            ('https://i.redd.it/test.gif', 'image/gif', 'gif'),
            ('https://v.redd.it/test', 'video/mp4', 'video'),
            ('https://imgur.com/test.gifv', 'video/mp4', 'video'),
            ('https://i.imgur.com/test.png', 'image/png', 'image'),
        ]

        for url, content_type, expected_type in test_cases:
            with self.subTest(url=url):
                result = downloader.detect_media_type(url, content_type)
                self.assertEqual(result, expected_type)

    def test_file_extension_detection(self):
        """Test enhanced file extension detection"""
        from media_downloader import MediaDownloader
        downloader = MediaDownloader()

        test_cases = [
            ('https://i.redd.it/test.jpg', 'image/jpeg', '.jpg'),
            ('https://i.redd.it/test.gif', 'image/gif', '.gif'),
            ('https://v.redd.it/test', 'video/mp4', '.mp4'),
            ('https://imgur.com/test.gifv', 'video/mp4', '.mp4'),
            ('https://i.imgur.com/test.png', 'image/png', '.png'),
            ('https://gfycat.com/test', None, '.mp4'),
        ]

        for url, content_type, expected_ext in test_cases:
            with self.subTest(url=url):
                result = downloader.get_file_extension(url, content_type)
                self.assertEqual(result, expected_ext)

    def test_url_prioritization(self):
        """Test URL prioritization functionality"""
        downloader = MediaDownloader()

        # Test URLs with different quality levels
        test_urls = [
            "https://preview.redd.it/test.jpg",  # Low priority (preview)
            "https://i.redd.it/test.jpg",        # High priority (original)
            "https://thumbs.redd.it/test.jpg",   # Should be filtered out
            "https://v.redd.it/test/DASH_720.mp4",  # High quality video
            "https://v.redd.it/test/DASH_240.mp4",  # Low quality video
        ]

        prioritized = downloader.prioritize_media_urls(test_urls)

        # Should filter out thumbnails
        self.assertNotIn("https://thumbs.redd.it/test.jpg", prioritized)

        # Should prioritize i.redd.it over preview.redd.it
        i_redd_index = next((i for i, url in enumerate(prioritized) if "i.redd.it" in url), -1)
        preview_index = next((i for i, url in enumerate(prioritized) if "preview.redd.it" in url), -1)

        if i_redd_index != -1 and preview_index != -1:
            self.assertLess(i_redd_index, preview_index)

    def test_quality_detection(self):
        """Test media quality detection"""
        downloader = MediaDownloader()

        test_cases = [
            ("https://v.redd.it/test/DASH_720.mp4", "720p", "high"),
            ("https://v.redd.it/test/DASH_480.mp4", "480p", "medium"),
            ("https://v.redd.it/test/DASH_240.mp4", "240p", "low"),
            ("https://i.redd.it/test.jpg", "unknown", "medium"),
        ]

        for url, expected_resolution, expected_quality in test_cases:
            with self.subTest(url=url):
                quality_info = downloader.detect_media_quality(url)
                self.assertEqual(quality_info['resolution'], expected_resolution)
                self.assertEqual(quality_info['estimated_quality'], expected_quality)

    def test_placeholder_detection(self):
        """Test placeholder content detection"""
        downloader = MediaDownloader()

        placeholder_urls = [
            "https://example.com/placeholder.jpg",
            "https://example.com/thumb/test.jpg",
            "https://example.com/loading.gif",
        ]

        normal_urls = [
            "https://i.redd.it/test.jpg",
            "https://i.imgur.com/test.png",
        ]

        for url in placeholder_urls:
            with self.subTest(url=url):
                self.assertTrue(downloader.is_placeholder_content(url))

        for url in normal_urls:
            with self.subTest(url=url):
                self.assertFalse(downloader.is_placeholder_content(url))

    def test_content_filtering(self):
        """Test content type and format filtering"""
        # Test with specific filters
        downloader = MediaDownloader(
            include_types=['image'],
            exclude_types=['video'],
            image_formats=['jpg', 'png'],
            min_file_size=2048
        )

        # Should allow images in allowed formats
        self.assertTrue(downloader.should_download_content(
            "https://i.redd.it/test.jpg", "image", "jpg"
        ))

        # Should reject videos (excluded)
        self.assertFalse(downloader.should_download_content(
            "https://v.redd.it/test.mp4", "video", "mp4"
        ))

        # Should reject images in disallowed formats
        self.assertFalse(downloader.should_download_content(
            "https://i.redd.it/test.gif", "image", "gif"
        ))

    def test_external_url_processing(self):
        """Test external media source URL processing"""
        downloader = MediaDownloader()

        # Test Gfycat URL pattern matching (without network calls)
        gfycat_url = "https://gfycat.com/testgif"
        # Just test that the URL pattern is recognized
        self.assertIn("gfycat.com", gfycat_url)

        # Test Redgifs URL pattern matching
        redgifs_url = "https://redgifs.com/watch/testgif"
        self.assertIn("redgifs.com", redgifs_url)

        # Test non-matching URLs (should return unchanged)
        normal_url = "https://i.redd.it/test.jpg"
        self.assertEqual(downloader.process_gfycat_url(normal_url), normal_url)
        self.assertEqual(downloader.process_redgifs_url(normal_url), normal_url)

    def test_media_only_mode(self):
        """Test media-only download mode functionality"""
        downloader = MediaDownloader(base_download_dir=self.temp_dir)

        # Test media-only directory creation
        media_dir = downloader.create_media_only_directory("testsubreddit")
        expected_path = Path(self.temp_dir) / "testsubreddit" / "media_only"
        self.assertEqual(media_dir, expected_path)
        self.assertTrue(media_dir.exists())

    def test_bundled_mode(self):
        """Test bundled post organization mode"""
        downloader = MediaDownloader(base_download_dir=self.temp_dir)

        # Test bundled directory creation
        media_dir = downloader.create_media_directory("testsubreddit", "test_post_123", bundled_mode=True)
        expected_path = Path(self.temp_dir) / "testsubreddit" / "posts" / "test_post_123"
        self.assertEqual(media_dir, expected_path)
        self.assertTrue(media_dir.exists())

    def test_configuration_loading(self):
        """Test configuration file loading and CLI argument parsing"""
        import tempfile
        import json
        from main import load_config_file

        # Test configuration file loading
        config_data = {
            "limit": 50,
            "include_types": ["image", "video"],
            "min_file_size": 2048,
            "quality_filter": "high"
        }

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f)
            config_path = f.name

        try:
            loaded_config = load_config_file(config_path)
            self.assertEqual(loaded_config['limit'], 50)
            self.assertEqual(loaded_config['include_types'], ["image", "video"])
            self.assertEqual(loaded_config['min_file_size'], 2048)
            self.assertEqual(loaded_config['quality_filter'], "high")
        finally:
            os.unlink(config_path)

    def test_resolution_parsing(self):
        """Test resolution string parsing"""
        from main import parse_resolution

        test_cases = [
            ("640x480", (640, 480)),
            ("1920x1080", (1920, 1080)),
            ("1280X720", (1280, 720)),  # Test case insensitive
        ]

        for resolution_str, expected in test_cases:
            with self.subTest(resolution_str=resolution_str):
                result = parse_resolution(resolution_str)
                self.assertEqual(result, expected)

        # Test invalid format
        with self.assertRaises(ValueError):
            parse_resolution("invalid")

    def test_argument_validation(self):
        """Test basic argument validation logic"""
        # Test conflicting include/exclude types
        include_types = ['image']
        exclude_types = ['image']

        # Should detect conflict
        has_conflict = any(t in exclude_types for t in include_types)
        self.assertTrue(has_conflict)

        # Test valid configuration
        include_types = ['image']
        exclude_types = ['video']
        has_conflict = any(t in exclude_types for t in include_types)
        self.assertFalse(has_conflict)

        # Test file size validation
        min_file_size = 1024
        max_file_size = 2048
        self.assertGreater(max_file_size, min_file_size)

        # Test invalid file size
        min_file_size = 2048
        max_file_size = 1024
        self.assertLess(max_file_size, min_file_size)  # This would be invalid

    def test_advanced_media_downloader_config(self):
        """Test MediaDownloader with advanced configuration options"""
        downloader = MediaDownloader(
            base_download_dir=self.temp_dir,
            include_types=['image', 'gif'],
            exclude_types=['video'],
            image_formats=['jpg', 'png'],
            min_file_size=2048,
            max_file_size=1024*1024,  # 1MB
            min_resolution=(640, 480),
            skip_placeholders=True,
            quality_filter='medium',
            max_retries=5,
            timeout=60
        )

        # Test configuration is properly set
        self.assertEqual(downloader.include_types, ['image', 'gif'])
        self.assertEqual(downloader.exclude_types, ['video'])
        self.assertEqual(downloader.image_formats, ['jpg', 'png'])
        self.assertEqual(downloader.min_file_size, 2048)
        self.assertEqual(downloader.max_file_size, 1024*1024)
        self.assertEqual(downloader.min_resolution, (640, 480))
        self.assertTrue(downloader.skip_placeholders)
        self.assertEqual(downloader.quality_filter, 'medium')
        self.assertEqual(downloader.max_retries, 5)
        self.assertEqual(downloader.request_timeout, 60)


def run_integration_test():
    """Run a simple integration test with a real subreddit"""
    print("Running integration test...")
    
    try:
        # Test with a small, reliable subreddit
        scraper = RedditScraper(rate_limit_delay=1.0)
        
        # Validate subreddit exists
        if not scraper.validate_subreddit_exists("test"):
            print("❌ Could not access test subreddit")
            return False
            
        # Get subreddit info
        info = scraper.get_subreddit_info("test")
        if info:
            print(f"✅ Subreddit info retrieved: {info['name']} ({info['subscribers']} subscribers)")
        
        # Scrape a few posts
        posts = scraper.scrape_subreddit("test", limit=5, sort="hot")
        
        if posts:
            print(f"✅ Successfully scraped {len(posts)} posts")
            
            # Test data storage
            temp_dir = tempfile.mkdtemp()
            try:
                storage = DataStorage(base_dir=temp_dir)
                json_path = storage.save_posts_json(posts, "test")
                csv_path = storage.save_posts_csv(posts, "test")
                
                print(f"✅ Data saved to JSON: {json_path}")
                print(f"✅ Data saved to CSV: {csv_path}")
                
                return True
                
            finally:
                shutil.rmtree(temp_dir, ignore_errors=True)
        else:
            print("❌ No posts scraped")
            return False
            
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False


if __name__ == '__main__':
    print("Reddit Scraper Test Suite")
    print("=" * 50)
    
    # Run unit tests
    print("\nRunning unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run integration test
    print("\n" + "=" * 50)
    success = run_integration_test()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
        sys.exit(1)
