#!/usr/bin/env python3
"""
Test script for Reddit Scraper
Tests various components and functionality
"""

import unittest
import tempfile
import shutil
from pathlib import Path
import json
import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from reddit_scraper import RedditScraper, RedditPost
from media_downloader import MediaDownloader
from data_storage import DataStorage
from config import Config


class TestRedditScraper(unittest.TestCase):
    """Test cases for Reddit Scraper functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.scraper = RedditScraper(rate_limit_delay=0.1)  # Faster for testing
        self.storage = DataStorage(base_dir=self.temp_dir)
        self.downloader = MediaDownloader(base_download_dir=self.temp_dir, rate_limit_delay=0.1)
        
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
    def test_sanitize_filename(self):
        """Test filename sanitization"""
        from media_downloader import MediaDownloader
        downloader = MediaDownloader()

        test_cases = [
            ("normal_filename.txt", "normal_filename.txt"),
            ("file with spaces.txt", "file_with_spaces.txt"),
            ("file<>:\"/\\|?*.txt", "file_________.txt"),
            ("very_long_filename_" + "x" * 200 + ".txt", "very_long_filename_" + "x" * 177 + ".txt"),
            ("", "unnamed"),
            ("...", "unnamed")
        ]

        for input_name, expected in test_cases:
            with self.subTest(input_name=input_name):
                result = downloader.sanitize_filename(input_name)
                self.assertEqual(result, expected)
                
    def test_config_validation(self):
        """Test configuration validation"""
        config = Config()
        self.assertTrue(config.validate_settings())
        
        # Test invalid settings
        config.set('rate_limit_delay', -1)
        self.assertFalse(config.validate_settings())
        
    def test_data_storage_json(self):
        """Test JSON data storage"""
        # Create sample posts
        posts = [
            RedditPost(
                title="Test Post 1",
                author="test_user",
                score=100,
                comments_count=50,
                created_utc="2024-01-01T00:00:00Z",
                post_type="text",
                url="https://reddit.com/test1",
                permalink="https://reddit.com/test1",
                content="Test content",
                media_urls=[],
                subreddit="test",
                post_id="test1"
            ),
            RedditPost(
                title="Test Post 2",
                author="test_user2",
                score=200,
                comments_count=75,
                created_utc="2024-01-01T01:00:00Z",
                post_type="image",
                url="https://i.redd.it/test.jpg",
                permalink="https://reddit.com/test2",
                content="",
                media_urls=["https://i.redd.it/test.jpg"],
                subreddit="test",
                post_id="test2"
            )
        ]
        
        # Save to JSON
        json_path = self.storage.save_posts_json(posts, "test", "test_posts.json")
        self.assertTrue(json_path.exists())
        
        # Load and verify
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        self.assertIn('metadata', data)
        self.assertIn('posts', data)
        self.assertEqual(len(data['posts']), 2)
        self.assertEqual(data['metadata']['total_posts'], 2)
        self.assertEqual(data['metadata']['subreddit'], "test")
        
    def test_data_storage_csv(self):
        """Test CSV data storage"""
        posts = [
            {
                'title': 'Test Post',
                'author': 'test_user',
                'score': 100,
                'comments_count': 50,
                'post_type': 'text',
                'media_urls': ['url1', 'url2']
            }
        ]
        
        csv_path = self.storage.save_posts_csv(posts, "test", "test_posts.csv")
        self.assertTrue(csv_path.exists())
        
        # Read and verify CSV
        import csv
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            rows = list(reader)
            
        self.assertEqual(len(rows), 1)
        self.assertEqual(rows[0]['title'], 'Test Post')
        self.assertEqual(rows[0]['media_urls'], 'url1; url2')  # Should be joined
        
    def test_media_downloader_url_processing(self):
        """Test media URL processing"""
        test_cases = [
            ("https://imgur.com/abc123", "https://imgur.com/abc123.jpg"),
            ("https://i.imgur.com/abc123.jpg", "https://i.imgur.com/abc123.jpg"),
            ("https://imgur.com/a/gallery123", "https://imgur.com/a/gallery123"),
            ("https://i.redd.it/test.jpg", "https://i.redd.it/test.jpg")
        ]
        
        for input_url, expected in test_cases:
            with self.subTest(input_url=input_url):
                result = self.downloader.process_imgur_url(input_url)
                self.assertEqual(result, expected)
                
    def test_media_downloader_downloadable_check(self):
        """Test downloadable URL detection"""
        downloadable_urls = [
            "https://i.redd.it/test.jpg",
            "https://i.imgur.com/test.png",
            "https://v.redd.it/test.mp4",
            "https://example.com/image.gif"
        ]
        
        non_downloadable_urls = [
            "https://youtube.com/watch?v=123",
            "https://twitter.com/status/123",
            "https://example.com/page.html"
        ]
        
        for url in downloadable_urls:
            with self.subTest(url=url):
                self.assertTrue(self.downloader.is_downloadable_url(url))
                
        for url in non_downloadable_urls:
            with self.subTest(url=url):
                self.assertFalse(self.downloader.is_downloadable_url(url))
                
    def test_directory_creation(self):
        """Test directory structure creation"""
        subreddit_dir = self.storage.create_subreddit_directory("testsubreddit")
        
        self.assertTrue(subreddit_dir.exists())
        self.assertTrue((subreddit_dir / "data").exists())
        self.assertTrue((subreddit_dir / "media").exists())
        self.assertTrue((subreddit_dir / "logs").exists())
        
    def test_summary_report_generation(self):
        """Test summary report generation"""
        posts = [
            {
                'title': 'Text Post',
                'author': 'user1',
                'score': 100,
                'comments_count': 50,
                'post_type': 'text',
                'media_urls': []
            },
            {
                'title': 'Image Post',
                'author': 'user2',
                'score': 200,
                'comments_count': 75,
                'post_type': 'image',
                'media_urls': ['https://i.redd.it/test.jpg']
            }
        ]
        
        media_stats = {
            'total_files': 5,
            'total_size': 1024 * 1024,  # 1MB
            'post_count': 2,
            'images': 3,
            'gifs': 1,
            'videos': 1,
            'other': 0
        }
        
        report_path = self.storage.create_summary_report(posts, "test", media_stats)
        self.assertTrue(report_path.exists())
        
        # Read and verify report content
        with open(report_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        self.assertIn("Total posts scraped: 2", content)
        self.assertIn("Unique authors: 2", content)
        self.assertIn("Posts with media: 1", content)
        self.assertIn("Total files downloaded: 5", content)
        self.assertIn("text: 1 (50.0%)", content)
        self.assertIn("image: 1 (50.0%)", content)
        self.assertIn("MEDIA TYPE BREAKDOWN:", content)
        self.assertIn("Images: 3", content)
        self.assertIn("GIFs: 1", content)
        self.assertIn("Videos: 1", content)

    def test_media_type_detection(self):
        """Test media type detection functionality"""
        from media_downloader import MediaDownloader
        downloader = MediaDownloader()

        # Test various media type detections
        test_cases = [
            ('https://i.redd.it/test.jpg', 'image/jpeg', 'image'),
            ('https://i.redd.it/test.gif', 'image/gif', 'gif'),
            ('https://v.redd.it/test', 'video/mp4', 'video'),
            ('https://imgur.com/test.gifv', 'video/mp4', 'video'),
            ('https://i.imgur.com/test.png', 'image/png', 'image'),
        ]

        for url, content_type, expected_type in test_cases:
            with self.subTest(url=url):
                result = downloader.detect_media_type(url, content_type)
                self.assertEqual(result, expected_type)

    def test_file_extension_detection(self):
        """Test enhanced file extension detection"""
        from media_downloader import MediaDownloader
        downloader = MediaDownloader()

        test_cases = [
            ('https://i.redd.it/test.jpg', 'image/jpeg', '.jpg'),
            ('https://i.redd.it/test.gif', 'image/gif', '.gif'),
            ('https://v.redd.it/test', 'video/mp4', '.mp4'),
            ('https://imgur.com/test.gifv', 'video/mp4', '.mp4'),
            ('https://i.imgur.com/test.png', 'image/png', '.png'),
            ('https://gfycat.com/test', None, '.mp4'),
        ]

        for url, content_type, expected_ext in test_cases:
            with self.subTest(url=url):
                result = downloader.get_file_extension(url, content_type)
                self.assertEqual(result, expected_ext)


def run_integration_test():
    """Run a simple integration test with a real subreddit"""
    print("Running integration test...")
    
    try:
        # Test with a small, reliable subreddit
        scraper = RedditScraper(rate_limit_delay=1.0)
        
        # Validate subreddit exists
        if not scraper.validate_subreddit_exists("test"):
            print("❌ Could not access test subreddit")
            return False
            
        # Get subreddit info
        info = scraper.get_subreddit_info("test")
        if info:
            print(f"✅ Subreddit info retrieved: {info['name']} ({info['subscribers']} subscribers)")
        
        # Scrape a few posts
        posts = scraper.scrape_subreddit("test", limit=5, sort="hot")
        
        if posts:
            print(f"✅ Successfully scraped {len(posts)} posts")
            
            # Test data storage
            temp_dir = tempfile.mkdtemp()
            try:
                storage = DataStorage(base_dir=temp_dir)
                json_path = storage.save_posts_json(posts, "test")
                csv_path = storage.save_posts_csv(posts, "test")
                
                print(f"✅ Data saved to JSON: {json_path}")
                print(f"✅ Data saved to CSV: {csv_path}")
                
                return True
                
            finally:
                shutil.rmtree(temp_dir, ignore_errors=True)
        else:
            print("❌ No posts scraped")
            return False
            
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False


if __name__ == '__main__':
    print("Reddit Scraper Test Suite")
    print("=" * 50)
    
    # Run unit tests
    print("\nRunning unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run integration test
    print("\n" + "=" * 50)
    success = run_integration_test()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
        sys.exit(1)
