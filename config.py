#!/usr/bin/env python3
"""
Configuration settings for Reddit Scraper
"""

import os
from pathlib import Path
from typing import Dict, Any


class Config:
    """Configuration class for Reddit Scraper"""
    
    # Default settings
    DEFAULT_SETTINGS = {
        'rate_limit_delay': 2.0,
        'max_retries': 3,
        'request_timeout': 30,
        'max_file_size_mb': 100,
        'max_posts_limit': 1000,
        'default_output_dir': 'downloads',
        'default_sort': 'hot',
        'default_limit': 100,
        'log_level': 'INFO',
        'user_agent': 'RedditScraper/1.0 (Educational/Research Purpose)',
        'supported_image_formats': ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'],
        'supported_video_formats': ['.mp4', '.webm', '.mov', '.avi', '.mkv'],
        'downloadable_domains': [
            'i.redd.it', 'i.imgur.com', 'imgur.com', 'v.redd.it',
            'gfycat.com', 'redgifs.com'
        ]
    }
    
    def __init__(self, config_file: str = None):
        """
        Initialize configuration
        
        Args:
            config_file: Path to configuration file (optional)
        """
        self.settings = self.DEFAULT_SETTINGS.copy()
        
        if config_file and Path(config_file).exists():
            self.load_from_file(config_file)
            
        # Override with environment variables
        self.load_from_env()
        
    def load_from_file(self, config_file: str):
        """Load configuration from file"""
        try:
            import json
            with open(config_file, 'r') as f:
                file_config = json.load(f)
                self.settings.update(file_config)
        except Exception as e:
            print(f"Warning: Could not load config file {config_file}: {e}")
            
    def load_from_env(self):
        """Load configuration from environment variables"""
        env_mappings = {
            'REDDIT_SCRAPER_RATE_LIMIT': 'rate_limit_delay',
            'REDDIT_SCRAPER_MAX_RETRIES': 'max_retries',
            'REDDIT_SCRAPER_TIMEOUT': 'request_timeout',
            'REDDIT_SCRAPER_MAX_FILE_SIZE': 'max_file_size_mb',
            'REDDIT_SCRAPER_OUTPUT_DIR': 'default_output_dir',
            'REDDIT_SCRAPER_LOG_LEVEL': 'log_level',
        }
        
        for env_var, setting_key in env_mappings.items():
            value = os.getenv(env_var)
            if value:
                # Convert to appropriate type
                if setting_key in ['rate_limit_delay', 'request_timeout']:
                    try:
                        self.settings[setting_key] = float(value)
                    except ValueError:
                        pass
                elif setting_key in ['max_retries', 'max_file_size_mb']:
                    try:
                        self.settings[setting_key] = int(value)
                    except ValueError:
                        pass
                else:
                    self.settings[setting_key] = value
                    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        return self.settings.get(key, default)
        
    def set(self, key: str, value: Any):
        """Set configuration value"""
        self.settings[key] = value
        
    def save_to_file(self, config_file: str):
        """Save current configuration to file"""
        try:
            import json
            with open(config_file, 'w') as f:
                json.dump(self.settings, f, indent=2)
        except Exception as e:
            print(f"Error saving config to {config_file}: {e}")
            
    def validate_settings(self) -> bool:
        """Validate configuration settings"""
        errors = []
        
        # Validate numeric settings
        if self.settings['rate_limit_delay'] < 0:
            errors.append("rate_limit_delay must be >= 0")
            
        if self.settings['max_retries'] < 1:
            errors.append("max_retries must be >= 1")
            
        if self.settings['request_timeout'] < 1:
            errors.append("request_timeout must be >= 1")
            
        if self.settings['max_file_size_mb'] < 1:
            errors.append("max_file_size_mb must be >= 1")
            
        # Validate directories
        output_dir = Path(self.settings['default_output_dir'])
        try:
            output_dir.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            errors.append(f"Cannot create output directory: {e}")
            
        if errors:
            print("Configuration validation errors:")
            for error in errors:
                print(f"  - {error}")
            return False
            
        return True
        
    def get_headers(self) -> Dict[str, str]:
        """Get HTTP headers for requests"""
        return {
            'User-Agent': self.settings['user_agent'],
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
        
    def is_supported_media_format(self, url: str) -> bool:
        """Check if URL points to supported media format"""
        url_lower = url.lower()
        
        supported_formats = (
            self.settings['supported_image_formats'] + 
            self.settings['supported_video_formats']
        )
        
        return any(url_lower.endswith(fmt) for fmt in supported_formats)
        
    def is_downloadable_domain(self, url: str) -> bool:
        """Check if URL is from a downloadable domain"""
        return any(domain in url for domain in self.settings['downloadable_domains'])
        
    def __str__(self) -> str:
        """String representation of configuration"""
        lines = ["Reddit Scraper Configuration:"]
        for key, value in sorted(self.settings.items()):
            lines.append(f"  {key}: {value}")
        return '\n'.join(lines)
