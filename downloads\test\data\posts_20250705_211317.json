{"metadata": {"subreddit": "test", "scraped_at": "2025-07-05T21:13:17.909453", "total_posts": 3, "scraper_version": "1.0"}, "posts": [{"title": "Some test commands", "author": "PitchforkAssistant", "score": 38, "comments_count": 1659, "created_utc": "2023-12-08T00:29:02+00:00", "post_type": "link", "url": "https://old.reddit.com/r/test/comments/18da1zl/some_test_commands/", "permalink": "https://reddit.com/r/test/comments/18da1zl/some_test_commands/", "content": "\n\nCommand | Description\n-------|-----------\n`!cqs` | Get your current [Contributor Quality Score](https://www.reddit.com/r/modnews/comments/16is6dh/contributor_quality_score_available_to_all/).\n`!ping` | pong\n`!autoremove` | Any post or comment containing this command will automatically be removed.\n`!remove` | Replying to your own post with this will cause it to be removed.\n\n\nLet me know if there are any others that might be useful for testing stuff.", "media_urls": [], "subreddit": "test", "post_id": "18da1zl", "upvote_ratio": 0.94, "flair": null}, {"title": "Test", "author": "Wonderful-Mark-2132", "score": 2, "comments_count": 1, "created_utc": "2025-07-06T00:22:36+00:00", "post_type": "link", "url": "https://old.reddit.com/r/test/comments/1lso71e/test/", "permalink": "https://reddit.com/r/test/comments/1lso71e/test/", "content": "Test*test.test", "media_urls": [], "subreddit": "test", "post_id": "1lso71e", "upvote_ratio": 1.0, "flair": null}, {"title": "Test", "author": "Nervous_Hat9782", "score": 2, "comments_count": 0, "created_utc": "2025-07-05T23:46:23+00:00", "post_type": "link", "url": "https://old.reddit.com/r/test/comments/1lsnhfe/test/", "permalink": "https://reddit.com/r/test/comments/1lsnhfe/test/", "content": "Im testing my account here!", "media_urls": [], "subreddit": "test", "post_id": "1lsnhfe", "upvote_ratio": 1.0, "flair": null}]}