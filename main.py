#!/usr/bin/env python3
"""
Reddit Scraper - Main CLI application
A comprehensive tool for scraping Reddit posts and media with respect to current API limitations.
"""

import argparse
import sys
import logging
import json
from pathlib import Path
from typing import Dict, Any, Optional

from reddit_scraper import RedditScraper
from media_downloader import MediaDownloader
from data_storage import DataStorage


def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None):
    """Setup logging configuration"""
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    handlers = [logging.StreamHandler(sys.stdout)]
    if log_file:
        handlers.append(logging.FileHandler(log_file))
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=handlers
    )


def load_config_file(config_path: str) -> Dict[str, Any]:
    """Load configuration from JSON file"""
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        return config
    except FileNotFoundError:
        print(f"Warning: Config file not found: {config_path}")
        return {}
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in config file: {e}")
        return {}

def validate_subreddit_name(subreddit: str) -> str:
    """Validate and clean subreddit name"""
    # Remove r/ prefix if present
    if subreddit.startswith('r/'):
        subreddit = subreddit[2:]

    # Basic validation
    if not subreddit or not subreddit.replace('_', '').replace('-', '').isalnum():
        raise ValueError(f"Invalid subreddit name: {subreddit}")

    return subreddit

def parse_resolution(resolution_str: str) -> tuple:
    """Parse resolution string like '640x480' into (width, height)"""
    try:
        width, height = resolution_str.lower().split('x')
        return int(width), int(height)
    except (ValueError, AttributeError):
        raise ValueError(f"Invalid resolution format: {resolution_str}. Use format like '640x480'")

def apply_config_file(args: argparse.Namespace, config: Dict[str, Any]) -> argparse.Namespace:
    """Apply configuration file settings to parsed arguments"""
    # Only apply config values if the argument wasn't explicitly set
    for key, value in config.items():
        if hasattr(args, key) and getattr(args, key) is None:
            setattr(args, key, value)
        elif hasattr(args, key) and isinstance(getattr(args, key), bool) and not getattr(args, key):
            # For boolean flags, only apply if they're False (not explicitly set)
            if value:
                setattr(args, key, value)
    return args


def main():
    """Main application entry point"""
    parser = argparse.ArgumentParser(
        description="Reddit Scraper - Extract posts and media from Reddit subreddits",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py python --limit 50 --sort hot
  python main.py MachineLearning --limit 100 --download-media --format both
  python main.py AskReddit --sort top --no-media --format csv
  
Note: Due to Reddit's limitations, maximum ~1000 posts can be scraped per subreddit.
        """
    )
    
    # Required arguments
    parser.add_argument(
        'subreddit',
        help='Subreddit name to scrape (without r/ prefix)'
    )
    
    # Optional arguments
    parser.add_argument(
        '--limit', '-l',
        type=int,
        default=100,
        help='Maximum number of posts to scrape (default: 100, max: ~1000)'
    )
    
    parser.add_argument(
        '--sort', '-s',
        choices=['hot', 'new', 'top', 'rising'],
        default='hot',
        help='Sort method for posts (default: hot)'
    )
    
    parser.add_argument(
        '--download-media', '-m',
        action='store_true',
        help='Download media files (images, videos, etc.)'
    )
    
    parser.add_argument(
        '--no-media',
        action='store_true',
        help='Skip media download (opposite of --download-media)'
    )

    parser.add_argument(
        '--media-only',
        action='store_true',
        help='Download only media files without post data or metadata (flat structure)'
    )

    parser.add_argument(
        '--bundled-posts',
        action='store_true',
        default=True,
        help='Organize each post in its own folder with media, text, and metadata (default: True)'
    )

    parser.add_argument(
        '--legacy-structure',
        action='store_true',
        help='Use legacy directory structure (separate data/ and media/ folders)'
    )
    
    parser.add_argument(
        '--format', '-f',
        choices=['json', 'csv', 'both'],
        default='json',
        help='Output format for post data (default: json)'
    )
    
    parser.add_argument(
        '--output-dir', '-o',
        default='downloads',
        help='Output directory for downloaded data (default: downloads)'
    )
    
    parser.add_argument(
        '--rate-limit',
        type=float,
        default=2.0,
        help='Delay between requests in seconds (default: 2.0)'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level (default: INFO)'
    )
    
    parser.add_argument(
        '--log-file',
        help='Log file path (default: logs to console only)'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be done without actually scraping'
    )

    # File type filtering options
    parser.add_argument(
        '--include-types',
        nargs='+',
        choices=['image', 'gif', 'video', 'text'],
        help='Include only specific content types (default: all types)'
    )

    parser.add_argument(
        '--exclude-types',
        nargs='+',
        choices=['image', 'gif', 'video', 'text'],
        help='Exclude specific content types'
    )

    parser.add_argument(
        '--image-formats',
        nargs='+',
        default=['jpg', 'jpeg', 'png', 'gif', 'webp'],
        help='Allowed image formats (default: jpg, jpeg, png, gif, webp)'
    )

    parser.add_argument(
        '--video-formats',
        nargs='+',
        default=['mp4', 'webm', 'mov'],
        help='Allowed video formats (default: mp4, webm, mov)'
    )

    # Quality and size options
    parser.add_argument(
        '--min-file-size',
        type=int,
        default=1024,
        help='Minimum file size in bytes (default: 1024)'
    )

    parser.add_argument(
        '--max-file-size',
        type=int,
        help='Maximum file size in bytes (no limit by default)'
    )

    parser.add_argument(
        '--min-resolution',
        help='Minimum resolution as WIDTHxHEIGHT (e.g., 640x480)'
    )

    parser.add_argument(
        '--skip-placeholders',
        action='store_true',
        default=True,
        help='Skip placeholder and thumbnail images (default: True)'
    )

    parser.add_argument(
        '--quality-filter',
        choices=['low', 'medium', 'high', 'very_high'],
        help='Filter by minimum quality level'
    )

    # Advanced options
    parser.add_argument(
        '--max-retries',
        type=int,
        default=3,
        help='Maximum download retries per file (default: 3)'
    )

    parser.add_argument(
        '--timeout',
        type=int,
        default=30,
        help='Request timeout in seconds (default: 30)'
    )

    parser.add_argument(
        '--user-agent',
        default='RedditScraper/1.0',
        help='Custom User-Agent string'
    )

    parser.add_argument(
        '--config-file',
        help='Load configuration from JSON file'
    )
    
    args = parser.parse_args()

    # Load configuration file if specified
    if args.config_file:
        config = load_config_file(args.config_file)
        args = apply_config_file(args, config)

    # Setup logging
    setup_logging(args.log_level, args.log_file)
    logger = logging.getLogger(__name__)

    try:
        # Validate inputs
        subreddit = validate_subreddit_name(args.subreddit)

        # Validate resolution if specified
        min_resolution = None
        if args.min_resolution:
            min_resolution = parse_resolution(args.min_resolution)

        # Validate file type filters
        if args.include_types and args.exclude_types:
            common_types = set(args.include_types) & set(args.exclude_types)
            if common_types:
                raise ValueError(f"Cannot both include and exclude the same types: {common_types}")

        # Validate file size limits
        if args.max_file_size and args.min_file_size and args.max_file_size < args.min_file_size:
            raise ValueError("Maximum file size cannot be smaller than minimum file size")
        
        if args.limit > 1000:
            logger.warning("Limit set above 1000. Reddit typically limits to ~1000 posts.")
            
        # Determine media download setting and modes
        download_media = args.download_media and not args.no_media
        media_only_mode = args.media_only
        bundled_mode = not args.legacy_structure

        if media_only_mode:
            download_media = True  # Force media download in media-only mode

        if args.dry_run:
            print(f"DRY RUN - Would scrape r/{subreddit}:")
            print(f"  Limit: {args.limit} posts")
            print(f"  Sort: {args.sort}")
            print(f"  Download media: {download_media}")
            print(f"  Media-only mode: {media_only_mode}")
            print(f"  Bundled posts: {bundled_mode}")
            print(f"  Output format: {args.format}")
            print(f"  Output directory: {args.output_dir}")
            print(f"  Rate limit: {args.rate_limit}s")
            return
            
        # Initialize components
        logger.info(f"Starting Reddit scraper for r/{subreddit}")
        
        scraper = RedditScraper(rate_limit_delay=args.rate_limit)
        storage = DataStorage(base_dir=args.output_dir)
        
        downloader = None
        if download_media:
            downloader = MediaDownloader(
                base_download_dir=args.output_dir,
                rate_limit_delay=args.rate_limit / 2,  # Faster for media downloads
                include_types=args.include_types,
                exclude_types=args.exclude_types,
                image_formats=args.image_formats,
                video_formats=args.video_formats,
                min_file_size=args.min_file_size,
                max_file_size=args.max_file_size,
                min_resolution=min_resolution,
                skip_placeholders=args.skip_placeholders,
                quality_filter=args.quality_filter,
                max_retries=args.max_retries,
                timeout=args.timeout,
                user_agent=args.user_agent
            )
        
        # Scrape posts
        logger.info(f"Scraping posts from r/{subreddit} (sort: {args.sort}, limit: {args.limit})")
        posts = scraper.scrape_subreddit(subreddit, limit=args.limit, sort=args.sort)
        
        if not posts:
            logger.error("No posts were scraped. Check subreddit name and network connection.")
            return 1
            
        logger.info(f"Successfully scraped {len(posts)} posts")
        
        # Download media if requested
        media_mapping = {}
        media_stats = None

        if download_media and downloader:
            logger.info("Downloading media files...")
            for post in posts:
                # Convert post to dictionary if needed
                from dataclasses import asdict
                if hasattr(post, '__dict__') and hasattr(post, '__dataclass_fields__'):
                    post_dict = asdict(post)
                elif isinstance(post, dict):
                    post_dict = post
                else:
                    # Fallback to __dict__ if available
                    post_dict = post.__dict__ if hasattr(post, '__dict__') else {}

                if post_dict.get('media_urls'):
                    downloaded = downloader.download_media_files(
                        post_dict, subreddit,
                        media_only_mode=media_only_mode,
                        bundled_mode=bundled_mode
                    )
                    if downloaded:
                        media_mapping[post_dict['post_id']] = downloaded

                # Save individual post data in bundled mode (not media-only)
                if bundled_mode and not media_only_mode:
                    storage.save_individual_post(post_dict, subreddit)

            # Get download statistics
            media_stats = downloader.get_download_stats(subreddit)
            logger.info(f"Downloaded {media_stats['total_files']} media files")

            # Save media mapping (skip in media-only mode)
            if media_mapping and not media_only_mode:
                storage.save_media_mapping(media_mapping, subreddit)
        
        # Save post data (skip in media-only mode)
        if not media_only_mode:
            if args.format in ['json', 'both']:
                json_path = storage.save_posts_json(posts, subreddit)
                logger.info(f"Saved posts data to: {json_path}")

            if args.format in ['csv', 'both']:
                csv_path = storage.save_posts_csv(posts, subreddit)
                logger.info(f"Saved posts data to: {csv_path}")

            # Create summary report
            report_path = storage.create_summary_report(posts, subreddit, media_stats)
            logger.info(f"Created summary report: {report_path}")
        else:
            logger.info("Media-only mode: Skipped post data and summary files")
        
        # Print summary
        print(f"\n{'='*50}")
        print(f"SCRAPING COMPLETED SUCCESSFULLY")
        print(f"{'='*50}")
        print(f"Subreddit: r/{subreddit}")
        print(f"Posts scraped: {len(posts)}")
        if download_media and media_stats:
            print(f"Media files downloaded: {media_stats['total_files']}")
            print(f"Total media size: {storage.format_file_size(media_stats['total_size'])}")
        print(f"Data saved to: {args.output_dir}/{subreddit}/")
        print(f"{'='*50}")
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("Scraping interrupted by user")
        return 1
        
    except Exception as e:
        logger.error(f"Error during scraping: {e}")
        if args.log_level == 'DEBUG':
            import traceback
            traceback.print_exc()
        return 1


if __name__ == '__main__':
    sys.exit(main())
