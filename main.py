#!/usr/bin/env python3
"""
Reddit Scraper - Main CLI application
A comprehensive tool for scraping Reddit posts and media with respect to current API limitations.
"""

import argparse
import sys
import logging
from pathlib import Path
from typing import Dict, Any

from reddit_scraper import RedditScraper
from media_downloader import MediaDownloader
from data_storage import DataStorage


def setup_logging(log_level: str = "INFO", log_file: str = None):
    """Setup logging configuration"""
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    handlers = [logging.StreamHandler(sys.stdout)]
    if log_file:
        handlers.append(logging.FileHandler(log_file))
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=handlers
    )


def validate_subreddit_name(subreddit: str) -> str:
    """Validate and clean subreddit name"""
    # Remove r/ prefix if present
    if subreddit.startswith('r/'):
        subreddit = subreddit[2:]
    
    # Basic validation
    if not subreddit or not subreddit.replace('_', '').replace('-', '').isalnum():
        raise ValueError(f"Invalid subreddit name: {subreddit}")
    
    return subreddit


def main():
    """Main application entry point"""
    parser = argparse.ArgumentParser(
        description="Reddit Scraper - Extract posts and media from Reddit subreddits",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py python --limit 50 --sort hot
  python main.py MachineLearning --limit 100 --download-media --format both
  python main.py AskReddit --sort top --no-media --format csv
  
Note: Due to Reddit's limitations, maximum ~1000 posts can be scraped per subreddit.
        """
    )
    
    # Required arguments
    parser.add_argument(
        'subreddit',
        help='Subreddit name to scrape (without r/ prefix)'
    )
    
    # Optional arguments
    parser.add_argument(
        '--limit', '-l',
        type=int,
        default=100,
        help='Maximum number of posts to scrape (default: 100, max: ~1000)'
    )
    
    parser.add_argument(
        '--sort', '-s',
        choices=['hot', 'new', 'top', 'rising'],
        default='hot',
        help='Sort method for posts (default: hot)'
    )
    
    parser.add_argument(
        '--download-media', '-m',
        action='store_true',
        help='Download media files (images, videos, etc.)'
    )
    
    parser.add_argument(
        '--no-media',
        action='store_true',
        help='Skip media download (opposite of --download-media)'
    )
    
    parser.add_argument(
        '--format', '-f',
        choices=['json', 'csv', 'both'],
        default='json',
        help='Output format for post data (default: json)'
    )
    
    parser.add_argument(
        '--output-dir', '-o',
        default='downloads',
        help='Output directory for downloaded data (default: downloads)'
    )
    
    parser.add_argument(
        '--rate-limit',
        type=float,
        default=2.0,
        help='Delay between requests in seconds (default: 2.0)'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level (default: INFO)'
    )
    
    parser.add_argument(
        '--log-file',
        help='Log file path (default: logs to console only)'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be done without actually scraping'
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level, args.log_file)
    logger = logging.getLogger(__name__)
    
    try:
        # Validate inputs
        subreddit = validate_subreddit_name(args.subreddit)
        
        if args.limit > 1000:
            logger.warning("Limit set above 1000. Reddit typically limits to ~1000 posts.")
            
        # Determine media download setting
        download_media = args.download_media and not args.no_media
        
        if args.dry_run:
            print(f"DRY RUN - Would scrape r/{subreddit}:")
            print(f"  Limit: {args.limit} posts")
            print(f"  Sort: {args.sort}")
            print(f"  Download media: {download_media}")
            print(f"  Output format: {args.format}")
            print(f"  Output directory: {args.output_dir}")
            print(f"  Rate limit: {args.rate_limit}s")
            return
            
        # Initialize components
        logger.info(f"Starting Reddit scraper for r/{subreddit}")
        
        scraper = RedditScraper(rate_limit_delay=args.rate_limit)
        storage = DataStorage(base_dir=args.output_dir)
        
        if download_media:
            downloader = MediaDownloader(
                base_download_dir=args.output_dir,
                rate_limit_delay=args.rate_limit / 2  # Faster for media downloads
            )
        
        # Scrape posts
        logger.info(f"Scraping posts from r/{subreddit} (sort: {args.sort}, limit: {args.limit})")
        posts = scraper.scrape_subreddit(subreddit, limit=args.limit, sort=args.sort)
        
        if not posts:
            logger.error("No posts were scraped. Check subreddit name and network connection.")
            return 1
            
        logger.info(f"Successfully scraped {len(posts)} posts")
        
        # Download media if requested
        media_mapping = {}
        media_stats = None
        
        if download_media:
            logger.info("Downloading media files...")
            for post in posts:
                post_dict = post.__dict__ if hasattr(post, '__dict__') else post
                if post_dict.get('media_urls'):
                    downloaded = downloader.download_media_files(post_dict, subreddit)
                    if downloaded:
                        media_mapping[post_dict['post_id']] = downloaded
                        
            # Get download statistics
            media_stats = downloader.get_download_stats(subreddit)
            logger.info(f"Downloaded {media_stats['total_files']} media files")
            
            # Save media mapping
            if media_mapping:
                storage.save_media_mapping(media_mapping, subreddit)
        
        # Save post data
        if args.format in ['json', 'both']:
            json_path = storage.save_posts_json(posts, subreddit)
            logger.info(f"Saved posts data to: {json_path}")
            
        if args.format in ['csv', 'both']:
            csv_path = storage.save_posts_csv(posts, subreddit)
            logger.info(f"Saved posts data to: {csv_path}")
            
        # Create summary report
        report_path = storage.create_summary_report(posts, subreddit, media_stats)
        logger.info(f"Created summary report: {report_path}")
        
        # Print summary
        print(f"\n{'='*50}")
        print(f"SCRAPING COMPLETED SUCCESSFULLY")
        print(f"{'='*50}")
        print(f"Subreddit: r/{subreddit}")
        print(f"Posts scraped: {len(posts)}")
        if download_media and media_stats:
            print(f"Media files downloaded: {media_stats['total_files']}")
            print(f"Total media size: {storage.format_file_size(media_stats['total_size'])}")
        print(f"Data saved to: {args.output_dir}/{subreddit}/")
        print(f"{'='*50}")
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("Scraping interrupted by user")
        return 1
        
    except Exception as e:
        logger.error(f"Error during scraping: {e}")
        if args.log_level == 'DEBUG':
            import traceback
            traceback.print_exc()
        return 1


if __name__ == '__main__':
    sys.exit(main())
