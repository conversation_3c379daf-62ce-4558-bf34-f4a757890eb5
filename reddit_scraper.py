#!/usr/bin/env python3
"""
Reddit Scraper - A comprehensive tool for scraping Reddit posts and media
Designed to work with Reddit's current limitations and API changes.
"""

import requests
import time
import json
import csv
import os
import re
import logging
from datetime import datetime, timezone
from urllib.parse import urljoin, urlparse
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict

from bs4 import BeautifulSoup
from tqdm import tqdm
import dateutil.parser


@dataclass
class RedditPost:
    """Data class to represent a Reddit post"""
    title: str
    author: str
    score: int
    comments_count: int
    created_utc: str
    post_type: str
    url: str
    permalink: str
    content: str
    media_urls: List[str]
    subreddit: str
    post_id: str
    upvote_ratio: Optional[float] = None
    flair: Optional[str] = None


class RedditScraper:
    """Main Reddit scraper class using web scraping techniques"""
    
    def __init__(self, rate_limit_delay: float = 2.0, max_retries: int = 3):
        """
        Initialize the Reddit scraper
        
        Args:
            rate_limit_delay: Delay between requests in seconds
            max_retries: Maximum number of retries for failed requests
        """
        self.rate_limit_delay = rate_limit_delay
        self.max_retries = max_retries
        self.session = requests.Session()
        
        # Set user agent to be respectful
        self.session.headers.update({
            'User-Agent': 'RedditScraper/1.0 (Educational/Research Purpose)',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
        
        # Setup logging
        self.setup_logging()
        
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('reddit_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for Windows compatibility"""
        # Remove or replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')

        # Replace spaces with underscores
        filename = filename.replace(' ', '_')

        # Limit length and remove trailing dots/spaces
        if len(filename) > 200:
            name, ext = os.path.splitext(filename)
            filename = name[:200-len(ext)] + ext

        filename = filename.strip('. ')
        return filename if filename else 'unnamed'
        
    def make_request(self, url: str) -> Optional[requests.Response]:
        """Make a request with retry logic and rate limiting"""
        for attempt in range(self.max_retries):
            try:
                time.sleep(self.rate_limit_delay)
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                return response
                
            except requests.exceptions.RequestException as e:
                self.logger.warning(f"Request failed (attempt {attempt + 1}/{self.max_retries}): {e}")
                if attempt == self.max_retries - 1:
                    self.logger.error(f"Failed to fetch {url} after {self.max_retries} attempts")
                    return None
                time.sleep(2 ** attempt)  # Exponential backoff
                
        return None
        
    def extract_post_data(self, post_element, subreddit: str) -> Optional[RedditPost]:
        """Extract data from a single post element"""
        try:
            # Extract basic post information
            title_elem = post_element.find('a', class_='title')
            if not title_elem:
                return None
                
            title = title_elem.get_text(strip=True)
            post_url = title_elem.get('href', '')
            
            # Handle relative URLs
            if post_url.startswith('/'):
                post_url = f"https://old.reddit.com{post_url}"
                
            # Extract post ID from URL
            post_id_match = re.search(r'/comments/([a-zA-Z0-9]+)/', post_url)
            post_id = post_id_match.group(1) if post_id_match else ''
            
            # Extract author
            author_elem = post_element.find('a', class_='author')
            author = author_elem.get_text(strip=True) if author_elem else '[deleted]'
            
            # Extract score
            score_elem = post_element.find('div', class_='score')
            score_text = score_elem.get_text(strip=True) if score_elem else '0'
            try:
                score = int(score_text.replace(',', ''))
            except (ValueError, AttributeError):
                score = 0
                
            # Extract comments count
            comments_elem = post_element.find('a', class_='comments')
            comments_text = comments_elem.get_text(strip=True) if comments_elem else '0 comments'
            comments_match = re.search(r'(\d+)', comments_text.replace(',', ''))
            comments_count = int(comments_match.group(1)) if comments_match else 0
            
            # Extract timestamp
            time_elem = post_element.find('time')
            created_utc = time_elem.get('datetime') if time_elem else datetime.now(timezone.utc).isoformat()
            
            # Determine post type and extract content
            post_type, content, media_urls = self.determine_post_type(post_element, post_url)
            
            # Extract flair
            flair_elem = post_element.find('span', class_='linkflairlabel')
            flair = flair_elem.get_text(strip=True) if flair_elem else None
            
            return RedditPost(
                title=title,
                author=author,
                score=score,
                comments_count=comments_count,
                created_utc=created_utc,
                post_type=post_type,
                url=post_url,
                permalink=post_url,
                content=content,
                media_urls=media_urls,
                subreddit=subreddit,
                post_id=post_id,
                flair=flair
            )
            
        except Exception as e:
            self.logger.error(f"Error extracting post data: {e}")
            return None
            
    def determine_post_type(self, post_element, post_url: str) -> Tuple[str, str, List[str]]:
        """Determine post type and extract content and media URLs"""
        media_urls = []
        content = ""
        post_type = "text"
        
        # Check for external link
        domain_elem = post_element.find('span', class_='domain')
        domain = domain_elem.get_text(strip=True) if domain_elem else ''
        
        # Check for thumbnail to identify media posts
        thumbnail_elem = post_element.find('img')
        if thumbnail_elem:
            thumbnail_url = thumbnail_elem.get('src', '')
            if thumbnail_url and not thumbnail_url.endswith(('default', 'self', 'nsfw')):
                media_urls.append(thumbnail_url)
        
        # Determine post type based on domain and URL patterns
        if 'i.redd.it' in post_url or 'i.imgur.com' in post_url:
            post_type = "image"
            media_urls.append(post_url)
        elif 'v.redd.it' in post_url or 'youtube.com' in post_url or 'youtu.be' in post_url:
            post_type = "video"
            media_urls.append(post_url)
        elif 'reddit.com/gallery/' in post_url:
            post_type = "gallery"
        elif domain and domain not in ['self.' + post_url.split('/r/')[-1].split('/')[0], 'reddit.com']:
            post_type = "link"
        
        # Extract text content for self posts
        if post_type == "text":
            expando_elem = post_element.find('div', class_='expando')
            if expando_elem:
                content = expando_elem.get_text(strip=True)
        
        return post_type, content, media_urls

    def scrape_subreddit(self, subreddit: str, limit: int = 100, sort: str = 'hot') -> List[RedditPost]:
        """
        Scrape posts from a subreddit

        Args:
            subreddit: Name of the subreddit (without r/)
            limit: Maximum number of posts to scrape (Reddit limits to ~1000)
            sort: Sort method ('hot', 'new', 'top', 'rising')

        Returns:
            List of RedditPost objects
        """
        posts = []
        after = None
        scraped_count = 0

        self.logger.info(f"Starting to scrape r/{subreddit} (sort: {sort}, limit: {limit})")

        with tqdm(total=min(limit, 1000), desc=f"Scraping r/{subreddit}") as pbar:
            while scraped_count < limit and scraped_count < 1000:  # Reddit's hard limit
                # Construct URL
                url = f"https://old.reddit.com/r/{subreddit}/{sort}/.json"
                params = {'limit': min(25, limit - scraped_count)}
                if after:
                    params['after'] = after

                # Make request
                response = self.make_request(url)
                if not response:
                    self.logger.error(f"Failed to fetch page for r/{subreddit}")
                    break

                try:
                    data = response.json()
                    post_listings = data['data']['children']

                    if not post_listings:
                        self.logger.info("No more posts found")
                        break

                    # Process each post
                    for post_data in post_listings:
                        if scraped_count >= limit:
                            break

                        post_info = post_data['data']

                        # Create RedditPost object from JSON data
                        post = self.create_post_from_json(post_info, subreddit)
                        if post:
                            posts.append(post)
                            scraped_count += 1
                            pbar.update(1)

                    # Get next page token
                    after = data['data']['after']
                    if not after:
                        self.logger.info("Reached end of available posts")
                        break

                except (json.JSONDecodeError, KeyError) as e:
                    self.logger.error(f"Error parsing JSON response: {e}")
                    break

        self.logger.info(f"Successfully scraped {len(posts)} posts from r/{subreddit}")
        return posts

    def create_post_from_json(self, post_data: dict, subreddit: str) -> Optional[RedditPost]:
        """Create RedditPost object from JSON data"""
        try:
            # Extract media URLs
            media_urls = []

            # Check for images
            if post_data.get('url'):
                url = post_data['url']
                if any(domain in url for domain in ['i.redd.it', 'i.imgur.com', 'imgur.com']):
                    media_urls.append(url)

            # Check for Reddit video
            if post_data.get('is_video') and post_data.get('media'):
                reddit_video = post_data['media'].get('reddit_video', {})
                # Try different video URL options
                video_url = (reddit_video.get('fallback_url') or
                           reddit_video.get('dash_url') or
                           reddit_video.get('hls_url'))
                if video_url:
                    media_urls.append(video_url)

            # Check for GIFs and other media in preview
            if post_data.get('preview') and post_data['preview'].get('images'):
                for image in post_data['preview']['images']:
                    if 'source' in image and 'url' in image['source']:
                        img_url = image['source']['url'].replace('&amp;', '&')
                        media_urls.append(img_url)

            # Check for external video/gif links
            url = post_data.get('url', '')
            if url and not any(url == existing for existing in media_urls):
                # Add GIF and video URLs
                if any(pattern in url.lower() for pattern in ['.gif', '.mp4', '.webm', 'gfycat', 'imgur.com/a/', 'v.redd.it']):
                    media_urls.append(url)

            # Check for gallery
            if post_data.get('is_gallery') and post_data.get('media_metadata'):
                for item_id, item_data in post_data['media_metadata'].items():
                    if 's' in item_data and 'u' in item_data['s']:
                        img_url = item_data['s']['u'].replace('&amp;', '&')
                        media_urls.append(img_url)

            # Determine post type
            post_type = "text"
            if post_data.get('is_video'):
                post_type = "video"
            elif post_data.get('is_gallery'):
                post_type = "gallery"
            elif media_urls:
                post_type = "image"
            elif post_data.get('url') and not post_data['url'].startswith(f"https://www.reddit.com/r/{subreddit}"):
                post_type = "link"

            # Convert timestamp
            created_utc = datetime.fromtimestamp(post_data['created_utc'], tz=timezone.utc).isoformat()

            return RedditPost(
                title=post_data.get('title', ''),
                author=post_data.get('author', '[deleted]'),
                score=post_data.get('score', 0),
                comments_count=post_data.get('num_comments', 0),
                created_utc=created_utc,
                post_type=post_type,
                url=post_data.get('url', ''),
                permalink=f"https://reddit.com{post_data.get('permalink', '')}",
                content=post_data.get('selftext', ''),
                media_urls=media_urls,
                subreddit=subreddit,
                post_id=post_data.get('id', ''),
                upvote_ratio=post_data.get('upvote_ratio'),
                flair=post_data.get('link_flair_text')
            )

        except Exception as e:
            self.logger.error(f"Error creating post from JSON: {e}")
            return None

    def handle_rate_limit(self, response: requests.Response) -> bool:
        """Handle rate limiting responses from Reddit"""
        if response.status_code == 429:
            retry_after = response.headers.get('Retry-After', '60')
            try:
                wait_time = int(retry_after)
                self.logger.warning(f"Rate limited. Waiting {wait_time} seconds...")
                time.sleep(wait_time)
                return True
            except ValueError:
                self.logger.warning("Rate limited. Waiting 60 seconds...")
                time.sleep(60)
                return True
        return False

    def validate_subreddit_exists(self, subreddit: str) -> bool:
        """Check if subreddit exists and is accessible"""
        try:
            url = f"https://old.reddit.com/r/{subreddit}/hot/.json?limit=1"
            response = self.make_request(url)

            if not response:
                return False

            data = response.json()

            # Check if subreddit is private or banned
            if 'error' in data:
                error_type = data.get('error')
                if error_type == 403:
                    self.logger.error(f"Subreddit r/{subreddit} is private or restricted")
                elif error_type == 404:
                    self.logger.error(f"Subreddit r/{subreddit} does not exist")
                else:
                    self.logger.error(f"Error accessing r/{subreddit}: {error_type}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"Error validating subreddit r/{subreddit}: {e}")
            return False

    def get_subreddit_info(self, subreddit: str) -> Optional[Dict]:
        """Get basic information about a subreddit"""
        try:
            url = f"https://old.reddit.com/r/{subreddit}/about/.json"
            response = self.make_request(url)

            if response:
                data = response.json()
                if 'data' in data:
                    subreddit_data = data['data']
                    return {
                        'name': subreddit_data.get('display_name', subreddit),
                        'title': subreddit_data.get('title', ''),
                        'description': subreddit_data.get('public_description', ''),
                        'subscribers': subreddit_data.get('subscribers', 0),
                        'created_utc': subreddit_data.get('created_utc', 0),
                        'over18': subreddit_data.get('over18', False)
                    }
        except Exception as e:
            self.logger.warning(f"Could not get subreddit info for r/{subreddit}: {e}")

        return None
