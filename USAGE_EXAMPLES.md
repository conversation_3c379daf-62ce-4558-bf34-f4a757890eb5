# Reddit Scraper - Usage Examples

## Quick Start Examples

### 1. Basic Text Post Scraping
```bash
# Scrape 50 posts from r/programming
python main.py programming --limit 50 --format json

# Scrape top posts from r/AskReddit
python main.py AskReddit --sort top --limit 100 --format csv
```

### 2. Media-Heavy Subreddits
```bash
# Download images from r/EarthPorn
python main.py EarthPorn --download-media --limit 25 --format both

# Scrape r/gifs with media download
python main.py gifs --download-media --limit 50 --rate-limit 3.0
```

### 3. Research and Analysis
```bash
# Large dataset collection
python main.py MachineLearning --limit 500 --format both --log-level INFO

# Multiple format export with detailed logging
python main.py datascience --limit 200 --format both --log-file research.log
```

### 4. Custom Configuration
```bash
# Slow, respectful scraping
python main.py science --limit 100 --rate-limit 5.0 --output-dir ./research_data

# Quick test run
python main.py test --limit 5 --dry-run
```

## Advanced Usage Scenarios

### Scenario 1: High-Quality Image Collection
```bash
# Collect only high-resolution images from r/EarthPorn
python main.py EarthPorn --media-only --include-types image \
  --min-resolution 1920x1080 --quality-filter high \
  --min-file-size 100000 --limit 50

# Download wallpapers with strict quality control
python main.py wallpapers --media-only --include-types image \
  --image-formats jpg png --min-resolution 2560x1440 \
  --skip-placeholders --quality-filter very_high
```

### Scenario 2: Video Content Curation
```bash
# Collect high-quality videos only
python main.py videos --media-only --include-types video \
  --video-formats mp4 --min-file-size 1048576 \
  --quality-filter high --max-retries 5

# Download GIFs and videos from r/funny
python main.py funny --media-only --include-types gif video \
  --exclude-types image --max-file-size 52428800
```

### Scenario 3: Academic Research with Configuration Files
```bash
# Create research_config.json with specific parameters
python main.py programming --config-file research_config.json \
  --limit 1000 --sort top --format both --log-level DEBUG

# Analyze machine learning trends with custom settings
python main.py MachineLearning --config-file ml_config.json \
  --limit 500 --format json --output-dir ./ml_research
```

### Scenario 4: Content Filtering and Organization
```bash
# Organized download with bundled posts
python main.py pics --bundled-posts --download-media \
  --include-types image gif --min-file-size 5000 \
  --limit 100

# Media-only mode with strict filtering
python main.py gifs --media-only --include-types gif \
  --min-resolution 640x480 --skip-placeholders \
  --quality-filter medium
```

### Scenario 2: Content Analysis
```bash
# Analyze meme trends with media
python main.py memes --download-media --limit 200 --format both

# Study art communities
python main.py Art --download-media --limit 100 --sort top --format json
```

### Scenario 3: Social Media Monitoring
```bash
# Monitor technology discussions
python main.py technology --limit 300 --format csv --rate-limit 2.5

# Track gaming community sentiment
python main.py gaming --limit 400 --format both --log-file gaming_monitor.log
```

## Output Analysis Examples

### JSON Data Structure
The JSON output contains rich metadata perfect for analysis:
```json
{
  "metadata": {
    "subreddit": "programming",
    "scraped_at": "2024-01-01T12:00:00Z",
    "total_posts": 100
  },
  "posts": [
    {
      "title": "Best practices for code reviews",
      "author": "developer123",
      "score": 1234,
      "comments_count": 89,
      "post_type": "text",
      "created_utc": "2024-01-01T10:00:00Z",
      "content": "Here are some tips...",
      "flair": "Discussion"
    }
  ]
}
```

### CSV Analysis
Perfect for spreadsheet analysis and data science:
- Import into Excel, Google Sheets, or Pandas
- Filter by post type, score, or date
- Analyze author activity and engagement

### Media Organization
Downloaded media is organized by post ID:
```
downloads/EarthPorn/media/
├── abc123/
│   └── Beautiful_Mountain_Sunset_[OC][4000x3000].jpg
└── def456/
    └── Northern_Lights_in_Iceland_[OC][3840x2160].jpg
```

## Performance Optimization

### For Large Datasets
```bash
# Optimize for speed (be respectful)
python main.py programming --limit 1000 --rate-limit 1.5 --format json

# Memory-efficient processing
python main.py datascience --limit 500 --no-media --format csv
```

### For Media-Heavy Subreddits
```bash
# Balanced media download
python main.py EarthPorn --download-media --limit 50 --rate-limit 2.0

# High-quality image collection
python main.py photography --download-media --limit 100 --format both
```

## Troubleshooting Common Issues

### Rate Limiting
```bash
# If you get rate limited, increase delay
python main.py popular --limit 100 --rate-limit 5.0

# For very active subreddits
python main.py AskReddit --limit 200 --rate-limit 3.0 --log-level DEBUG
```

### Private/Restricted Subreddits
```bash
# Test subreddit accessibility first
python main.py private_subreddit --limit 1 --dry-run

# Check subreddit exists
python main.py nonexistent --limit 5 --log-level DEBUG
```

### Network Issues
```bash
# Increase timeout and retries via environment
set REDDIT_SCRAPER_TIMEOUT=60
set REDDIT_SCRAPER_MAX_RETRIES=5
python main.py programming --limit 100
```

## Data Analysis Workflows

### Python Analysis Example
```python
import json
import pandas as pd

# Load scraped data
with open('downloads/programming/data/posts_20240101_120000.json', 'r') as f:
    data = json.load(f)

# Convert to DataFrame
df = pd.DataFrame(data['posts'])

# Analyze top posts
top_posts = df.nlargest(10, 'score')
print(top_posts[['title', 'score', 'comments_count']])

# Analyze post types
post_type_counts = df['post_type'].value_counts()
print(post_type_counts)
```

### Excel Analysis
1. Open the CSV file in Excel
2. Create pivot tables for post type analysis
3. Use filters to find high-engagement posts
4. Create charts for temporal analysis

## Best Practices

### Ethical Scraping
- Always use appropriate rate limits (2+ seconds)
- Respect Reddit's terms of service
- Don't overwhelm servers with requests
- Use for research/educational purposes

### Data Management
- Organize output directories by project
- Use descriptive filenames with timestamps
- Backup important datasets
- Document your scraping parameters

### Quality Control
- Always review summary reports
- Check for missing or corrupted media files
- Validate data integrity with test runs
- Monitor log files for errors

## Integration Examples

### With Data Science Tools
```bash
# Scrape for Jupyter notebook analysis
python main.py datascience --limit 300 --format json --output-dir ./notebooks/data

# Prepare data for machine learning
python main.py MachineLearning --limit 1000 --format csv --no-media
```

### With Content Management
```bash
# Collect content with media for websites
python main.py photography --download-media --limit 50 --format both

# Archive community discussions
python main.py community_name --limit 500 --format json --log-file archive.log
```

## Automation Scripts

### Batch Processing
```bash
# Create a batch script for multiple subreddits
echo "python main.py programming --limit 100 --format json" > scrape_batch.bat
echo "python main.py datascience --limit 100 --format json" >> scrape_batch.bat
echo "python main.py MachineLearning --limit 100 --format json" >> scrape_batch.bat
```

### Scheduled Collection
```bash
# Daily collection script
python main.py technology --limit 50 --format both --output-dir ./daily_$(date +%Y%m%d)
```

Remember: Always use this tool responsibly and in compliance with Reddit's terms of service!
