#!/usr/bin/env python3
"""
Data Storage - Module for saving and organizing Reddit post data
"""

import json
import csv
import os
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging
from dataclasses import asdict


class DataStorage:
    """Handles saving and organizing Reddit post data in various formats"""
    
    def __init__(self, base_dir: str = "downloads"):
        """
        Initialize the data storage manager
        
        Args:
            base_dir: Base directory for all data storage
        """
        self.base_dir = Path(base_dir)
        self.logger = logging.getLogger(__name__)
        
        # Create base directory
        self.base_dir.mkdir(exist_ok=True)
        
    def create_subreddit_directory(self, subreddit: str) -> Path:
        """Create directory structure for a subreddit"""
        subreddit_dir = self.base_dir / subreddit
        subreddit_dir.mkdir(exist_ok=True)
        
        # Create subdirectories
        (subreddit_dir / "data").mkdir(exist_ok=True)
        (subreddit_dir / "media").mkdir(exist_ok=True)
        (subreddit_dir / "logs").mkdir(exist_ok=True)
        
        return subreddit_dir
        
    def save_posts_json(self, posts: List[Any], subreddit: str, filename: str = None) -> Path:
        """
        Save posts data to JSON file
        
        Args:
            posts: List of RedditPost objects or dictionaries
            subreddit: Subreddit name
            filename: Custom filename (optional)
            
        Returns:
            Path to saved file
        """
        subreddit_dir = self.create_subreddit_directory(subreddit)
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"posts_{timestamp}.json"
            
        filepath = subreddit_dir / "data" / filename
        
        # Convert posts to dictionaries if they're dataclass objects
        posts_data = []
        for post in posts:
            if hasattr(post, '__dict__'):
                posts_data.append(asdict(post))
            else:
                posts_data.append(post)
        
        # Create metadata
        metadata = {
            "subreddit": subreddit,
            "scraped_at": datetime.now().isoformat(),
            "total_posts": len(posts_data),
            "scraper_version": "1.0"
        }
        
        # Combine metadata and posts
        output_data = {
            "metadata": metadata,
            "posts": posts_data
        }
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
                
            self.logger.info(f"Saved {len(posts_data)} posts to {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error saving JSON file: {e}")
            raise
            
    def save_posts_csv(self, posts: List[Any], subreddit: str, filename: str = None) -> Path:
        """
        Save posts data to CSV file
        
        Args:
            posts: List of RedditPost objects or dictionaries
            subreddit: Subreddit name
            filename: Custom filename (optional)
            
        Returns:
            Path to saved file
        """
        subreddit_dir = self.create_subreddit_directory(subreddit)
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"posts_{timestamp}.csv"
            
        filepath = subreddit_dir / "data" / filename
        
        if not posts:
            self.logger.warning("No posts to save to CSV")
            return filepath
            
        # Convert posts to dictionaries if they're dataclass objects
        posts_data = []
        for post in posts:
            if hasattr(post, '__dict__'):
                post_dict = asdict(post)
            else:
                post_dict = post.copy()
                
            # Convert list fields to strings for CSV compatibility
            if 'media_urls' in post_dict and isinstance(post_dict['media_urls'], list):
                post_dict['media_urls'] = '; '.join(post_dict['media_urls'])
                
            posts_data.append(post_dict)
        
        try:
            # Get fieldnames from first post
            fieldnames = list(posts_data[0].keys())
            
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(posts_data)
                
            self.logger.info(f"Saved {len(posts_data)} posts to {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error saving CSV file: {e}")
            raise
            
    def save_media_mapping(self, media_mapping: Dict[str, Dict[str, str]], subreddit: str) -> Path:
        """
        Save media URL to local file mapping
        
        Args:
            media_mapping: Dictionary mapping post IDs to their media files
            subreddit: Subreddit name
            
        Returns:
            Path to saved mapping file
        """
        subreddit_dir = self.create_subreddit_directory(subreddit)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filepath = subreddit_dir / "data" / f"media_mapping_{timestamp}.json"
        
        mapping_data = {
            "subreddit": subreddit,
            "created_at": datetime.now().isoformat(),
            "total_posts_with_media": len(media_mapping),
            "mapping": media_mapping
        }
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(mapping_data, f, indent=2, ensure_ascii=False)
                
            self.logger.info(f"Saved media mapping to {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error saving media mapping: {e}")
            raise
            
    def create_summary_report(self, posts: List[Any], subreddit: str, 
                            media_stats: Dict[str, int] = None) -> Path:
        """
        Create a summary report of the scraping session
        
        Args:
            posts: List of scraped posts
            subreddit: Subreddit name
            media_stats: Media download statistics
            
        Returns:
            Path to summary report file
        """
        subreddit_dir = self.create_subreddit_directory(subreddit)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filepath = subreddit_dir / "data" / f"summary_report_{timestamp}.txt"
        
        # Calculate statistics
        total_posts = len(posts)
        post_types = {}
        authors = set()
        total_score = 0
        total_comments = 0
        posts_with_media = 0
        
        for post in posts:
            if hasattr(post, '__dict__'):
                post_dict = asdict(post)
            else:
                post_dict = post
                
            # Count post types
            post_type = post_dict.get('post_type', 'unknown')
            post_types[post_type] = post_types.get(post_type, 0) + 1
            
            # Collect authors
            author = post_dict.get('author', '')
            if author and author != '[deleted]':
                authors.add(author)
                
            # Sum scores and comments
            total_score += post_dict.get('score', 0)
            total_comments += post_dict.get('comments_count', 0)
            
            # Count posts with media
            media_urls = post_dict.get('media_urls', [])
            if media_urls:
                posts_with_media += 1
        
        # Create report content
        report_lines = [
            f"Reddit Scraper Summary Report",
            f"=" * 50,
            f"Subreddit: r/{subreddit}",
            f"Scraped at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"",
            f"POST STATISTICS:",
            f"  Total posts scraped: {total_posts}",
            f"  Unique authors: {len(authors)}",
            f"  Posts with media: {posts_with_media}",
            f"  Total score: {total_score:,}",
            f"  Total comments: {total_comments:,}",
            f"",
            f"POST TYPES:",
        ]
        
        for post_type, count in sorted(post_types.items()):
            percentage = (count / total_posts * 100) if total_posts > 0 else 0
            report_lines.append(f"  {post_type}: {count} ({percentage:.1f}%)")
            
        if media_stats:
            report_lines.extend([
                f"",
                f"MEDIA DOWNLOAD STATISTICS:",
                f"  Total files downloaded: {media_stats.get('total_files', 0)}",
                f"  Total size: {self.format_file_size(media_stats.get('total_size', 0))}",
                f"  Posts with downloaded media: {media_stats.get('post_count', 0)}",
                f"",
                f"MEDIA TYPE BREAKDOWN:",
                f"  Images: {media_stats.get('images', 0)}",
                f"  GIFs: {media_stats.get('gifs', 0)}",
                f"  Videos: {media_stats.get('videos', 0)}",
                f"  Other: {media_stats.get('other', 0)}",
            ])
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report_lines))
                
            self.logger.info(f"Created summary report: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error creating summary report: {e}")
            raise
            
    def format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0 B"
            
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
            
        return f"{size_bytes:.1f} {size_names[i]}"
        
    def load_posts_json(self, filepath: Path) -> Dict[str, Any]:
        """Load posts data from JSON file"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data
        except Exception as e:
            self.logger.error(f"Error loading JSON file {filepath}: {e}")
            raise
            
    def list_saved_data(self, subreddit: str) -> Dict[str, List[Path]]:
        """List all saved data files for a subreddit"""
        subreddit_dir = self.base_dir / subreddit / "data"
        
        if not subreddit_dir.exists():
            return {"json": [], "csv": [], "reports": [], "mappings": []}
            
        files = {
            "json": [],
            "csv": [], 
            "reports": [],
            "mappings": []
        }
        
        for file_path in subreddit_dir.iterdir():
            if file_path.is_file():
                if file_path.suffix == '.json':
                    if 'mapping' in file_path.name:
                        files["mappings"].append(file_path)
                    else:
                        files["json"].append(file_path)
                elif file_path.suffix == '.csv':
                    files["csv"].append(file_path)
                elif file_path.suffix == '.txt':
                    files["reports"].append(file_path)
                    
        return files
