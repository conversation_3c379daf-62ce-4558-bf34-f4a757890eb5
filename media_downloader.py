#!/usr/bin/env python3
"""
Media Downloader - Module for downloading and organizing Reddit media files
"""

import os
import requests
import mimetypes
from pathlib import Path
from urllib.parse import urlparse, unquote
from typing import Dict, List, Optional, Any, Tuple
import logging
from PIL import Image
import time
import json
import re


class MediaDownloader:
    """Handles downloading and organizing media files from Reddit posts"""
    
    def __init__(self, base_download_dir: str = "downloads", rate_limit_delay: float = 1.0,
                 include_types: Optional[List[str]] = None, exclude_types: Optional[List[str]] = None,
                 image_formats: Optional[List[str]] = None, video_formats: Optional[List[str]] = None,
                 min_file_size: int = 1024, max_file_size: Optional[int] = None,
                 min_resolution: Optional[tuple] = None, skip_placeholders: bool = True,
                 quality_filter: Optional[str] = None, max_retries: int = 3,
                 timeout: int = 30, user_agent: str = 'RedditScraper/1.0'):
        """
        Initialize the media downloader

        Args:
            base_download_dir: Base directory for downloads
            rate_limit_delay: Delay between downloads in seconds
            include_types: List of content types to include
            exclude_types: List of content types to exclude
            image_formats: Allowed image formats
            video_formats: Allowed video formats
            min_file_size: Minimum file size in bytes
            max_file_size: Maximum file size in bytes
            min_resolution: Minimum resolution as (width, height) tuple
            skip_placeholders: Whether to skip placeholder images
            quality_filter: Minimum quality level filter
            max_retries: Maximum download retries per file
            timeout: Request timeout in seconds
            user_agent: Custom User-Agent string
        """
        self.base_download_dir = Path(base_download_dir)
        self.rate_limit_delay = rate_limit_delay
        self.session = requests.Session()

        # Configuration options
        self.include_types = include_types or ['image', 'gif', 'video']
        self.exclude_types = exclude_types or []
        self.image_formats = [fmt.lower() for fmt in (image_formats or ['jpg', 'jpeg', 'png', 'gif', 'webp'])]
        self.video_formats = [fmt.lower() for fmt in (video_formats or ['mp4', 'webm', 'mov'])]
        self.min_file_size = min_file_size
        self.max_file_size = max_file_size
        self.min_resolution = min_resolution
        self.skip_placeholders = skip_placeholders
        self.quality_filter = quality_filter
        self.max_retries = max_retries
        self.timeout = timeout

        # Set user agent
        self.session.headers.update({
            'User-Agent': user_agent,
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })

        # Store timeout for use in requests
        self.request_timeout = timeout

        self.logger = logging.getLogger(__name__)

        # Create base download directory
        self.base_download_dir.mkdir(exist_ok=True)
        
    def sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for Windows compatibility"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Remove or replace other problematic characters
        filename = filename.replace(' ', '_')
        filename = filename.strip('. ')
        
        # Limit length
        if len(filename) > 200:
            name, ext = os.path.splitext(filename)
            filename = name[:200-len(ext)] + ext
            
        return filename if filename else 'unnamed'
        
    def get_file_extension(self, url: str, content_type: Optional[str] = None) -> str:
        """Determine file extension from URL or content type"""
        # Try to get extension from URL
        parsed_url = urlparse(url)
        path = unquote(parsed_url.path)
        _, ext = os.path.splitext(path)

        # Known media extensions
        known_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff',
                           '.mp4', '.webm', '.mov', '.avi', '.mkv', '.flv', '.wmv', '.m4v']

        if ext and ext.lower() in known_extensions:
            return ext.lower()

        # Try to get extension from content type
        if content_type:
            content_type_clean = content_type.split(';')[0].strip()
            ext = mimetypes.guess_extension(content_type_clean)
            if ext and ext.lower() in known_extensions:
                return ext.lower()

            # Manual mapping for common types
            content_type_map = {
                'image/jpeg': '.jpg',
                'image/jpg': '.jpg',
                'image/png': '.png',
                'image/gif': '.gif',
                'image/webp': '.webp',
                'video/mp4': '.mp4',
                'video/webm': '.webm',
                'video/quicktime': '.mov',
                'video/x-msvideo': '.avi'
            }
            if content_type_clean in content_type_map:
                return content_type_map[content_type_clean]

        # Default extensions based on common Reddit media domains
        if 'i.redd.it' in url:
            # Check if it might be a GIF based on URL patterns
            if 'gif' in url.lower():
                return '.gif'
            return '.jpg'
        elif 'v.redd.it' in url:
            return '.mp4'
        elif 'imgur.com' in url:
            # Imgur can host various formats
            if 'gifv' in url:
                return '.mp4'  # Imgur's gifv is actually mp4
            elif 'gif' in url.lower():
                return '.gif'
            return '.jpg'
        elif 'gfycat.com' in url or 'redgifs.com' in url:
            return '.mp4'

        return '.unknown'
        
    def create_media_directory(self, subreddit: str, post_id: str, bundled_mode: bool = True) -> Path:
        """Create organized directory structure for media files"""
        if bundled_mode:
            # New bundled structure - media goes in post folder
            media_dir = self.base_download_dir / subreddit / "posts" / post_id
        else:
            # Legacy structure for compatibility
            media_dir = self.base_download_dir / subreddit / "media" / post_id
        media_dir.mkdir(parents=True, exist_ok=True)
        return media_dir

    def create_media_only_directory(self, subreddit: str) -> Path:
        """Create flat directory structure for media-only mode"""
        media_dir = self.base_download_dir / subreddit / "media_only"
        media_dir.mkdir(parents=True, exist_ok=True)
        return media_dir
        
    def download_file(self, url: str, filepath: Path, max_retries: int = 3) -> bool:
        """
        Download a file from URL to filepath
        
        Args:
            url: URL to download from
            filepath: Local filepath to save to
            max_retries: Maximum number of retry attempts
            
        Returns:
            True if successful, False otherwise
        """
        for attempt in range(max_retries):
            try:
                time.sleep(self.rate_limit_delay)
                
                response = self.session.get(url, stream=True, timeout=30)
                response.raise_for_status()
                
                # Check file size (limit to 100MB)
                content_length = response.headers.get('content-length')
                if content_length and int(content_length) > 100 * 1024 * 1024:
                    self.logger.warning(f"File too large ({content_length} bytes): {url}")
                    return False
                
                # Download file
                with open(filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            
                self.logger.info(f"Downloaded: {filepath.name}")
                return True
                
            except requests.exceptions.RequestException as e:
                self.logger.warning(f"Download failed (attempt {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    self.logger.error(f"Failed to download {url} after {max_retries} attempts")
                    return False
                time.sleep(2 ** attempt)  # Exponential backoff
                
            except Exception as e:
                self.logger.error(f"Unexpected error downloading {url}: {e}")
                return False
                
        return False
        
    def process_imgur_url(self, url: str) -> List[str]:
        """Process Imgur URLs to get direct image links, returns list for galleries"""
        if 'imgur.com' not in url:
            return [url]

        # Already a direct link
        if url.endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp', '.mp4', '.webm')):
            return [url]

        # Handle .gifv conversion
        if '.gifv' in url:
            return [url.replace('.gifv', '.mp4')]

        # Handle albums and galleries
        if '/a/' in url or '/gallery/' in url:
            return self.extract_imgur_gallery(url)

        # Single image - try to get direct URL with quality detection
        if url.endswith('/'):
            url = url[:-1]
        image_id = url.split('/')[-1].split('.')[0]

        # Try multiple formats, prioritize original quality
        potential_urls = [
            f"https://i.imgur.com/{image_id}.png",
            f"https://i.imgur.com/{image_id}.jpg",
            f"https://i.imgur.com/{image_id}.gif"
        ]

        # Return the first working URL
        for test_url in potential_urls:
            try:
                response = requests.head(test_url, timeout=10)
                if response.status_code == 200:
                    return [test_url]
            except:
                continue

        # Fallback to jpg
        return [f"https://i.imgur.com/{image_id}.jpg"]

    def extract_imgur_gallery(self, url: str) -> List[str]:
        """Extract individual image URLs from Imgur gallery/album"""
        try:
            # For now, return the original URL - full gallery extraction would require
            # parsing the Imgur page HTML or using their API
            self.logger.info(f"Imgur gallery detected: {url} (individual extraction not yet implemented)")
            return [url]
        except Exception as e:
            self.logger.error(f"Error extracting Imgur gallery: {e}")
            return [url]

    def process_reddit_video_url(self, url: str) -> str:
        """Process Reddit video URLs to get the best quality video"""
        if 'v.redd.it' in url:
            # Reddit video URLs often need DASH_720.mp4 or similar appended
            if not url.endswith('.mp4'):
                # Try different quality options
                quality_options = ['DASH_720.mp4', 'DASH_480.mp4', 'DASH_360.mp4', 'DASH_240.mp4']
                for quality in quality_options:
                    test_url = f"{url}/{quality}"
                    try:
                        response = self.session.head(test_url, timeout=5)
                        if response.status_code == 200:
                            return test_url
                    except:
                        continue
                # If no quality works, try the original URL
                return url
        return url

    def process_gfycat_url(self, url: str) -> str:
        """Process Gfycat URLs to get direct MP4 links"""
        if 'gfycat.com' in url:
            try:
                # Extract gfy ID from URL
                gfy_id = url.split('/')[-1].split('.')[0]
                # Try direct MP4 URL pattern
                direct_url = f"https://giant.gfycat.com/{gfy_id}.mp4"

                # Test if direct URL works
                response = self.session.head(direct_url, timeout=10)
                if response.status_code == 200:
                    return direct_url

                # Fallback to webm
                webm_url = f"https://giant.gfycat.com/{gfy_id}.webm"
                response = self.session.head(webm_url, timeout=10)
                if response.status_code == 200:
                    return webm_url

            except Exception as e:
                self.logger.warning(f"Error processing Gfycat URL {url}: {e}")

        return url

    def process_redgifs_url(self, url: str) -> str:
        """Process Redgifs URLs to get direct video links"""
        if 'redgifs.com' in url:
            try:
                # Extract gif ID from URL
                gif_id = url.split('/')[-1].split('.')[0]
                # Try direct MP4 URL pattern
                direct_url = f"https://thumbs2.redgifs.com/{gif_id}.mp4"

                # Test if direct URL works
                response = self.session.head(direct_url, timeout=10)
                if response.status_code == 200:
                    return direct_url

            except Exception as e:
                self.logger.warning(f"Error processing Redgifs URL {url}: {e}")

        return url

    def detect_media_type(self, url: str, content_type: Optional[str] = None) -> str:
        """Detect if media is image, gif, or video"""
        # Check content type first
        if content_type:
            if content_type.startswith('video/'):
                return 'video'
            elif content_type.startswith('image/gif'):
                return 'gif'
            elif content_type.startswith('image/'):
                return 'image'

        # Check URL patterns
        if any(pattern in url.lower() for pattern in ['.mp4', '.webm', '.mov', '.avi', 'v.redd.it']):
            return 'video'
        elif '.gif' in url.lower() or 'gif' in url.lower():
            return 'gif'
        elif any(pattern in url.lower() for pattern in ['.jpg', '.jpeg', '.png', '.webp', 'i.redd.it']):
            return 'image'

        return 'unknown'
        
    def download_media_files(self, post_data: dict, subreddit: str,
                           media_only_mode: bool = False, bundled_mode: bool = True) -> Dict[str, str]:
        """
        Download all media files for a post

        Args:
            post_data: Dictionary containing post information
            subreddit: Subreddit name
            media_only_mode: If True, use flat directory structure with descriptive names
            bundled_mode: If True, organize files in post-specific folders

        Returns:
            Dictionary mapping original URLs to local file paths
        """
        media_urls = post_data.get('media_urls', [])
        post_id = post_data.get('post_id', 'unknown')
        post_title = post_data.get('title', 'untitled')

        if not media_urls:
            return {}

        # Prioritize URLs to get best quality sources and eliminate thumbnails
        prioritized_urls = self.prioritize_media_urls(media_urls)
        if not prioritized_urls:
            self.logger.info(f"No downloadable media URLs found after filtering")
            return {}

        # Create media directory based on mode
        if media_only_mode:
            media_dir = self.create_media_only_directory(subreddit)
        else:
            media_dir = self.create_media_directory(subreddit, post_id, bundled_mode)

        downloaded_files = {}

        for i, url in enumerate(prioritized_urls):
            try:
                # Process special URLs - imgur may return multiple URLs for galleries
                imgur_urls = self.process_imgur_url(url)

                # Process each URL from imgur (usually just one, but could be multiple for galleries)
                for imgur_url in imgur_urls:
                    # Apply all URL processing methods in sequence
                    processed_url = self.process_reddit_video_url(imgur_url)
                    processed_url = self.process_gfycat_url(processed_url)
                    processed_url = self.process_redgifs_url(processed_url)

                    # Skip if URL is not downloadable
                    if not self.is_downloadable_url(processed_url):
                        self.logger.info(f"Skipping non-downloadable URL: {processed_url}")
                        continue

                    # Get content type and file extension
                    try:
                        response = self.session.head(processed_url, timeout=10)
                        content_type = response.headers.get('content-type', '')
                    except:
                        content_type = ''

                    extension = self.get_file_extension(processed_url, content_type)
                    media_type = self.detect_media_type(processed_url, content_type)

                    # Check if content should be downloaded based on filters
                    if not self.should_download_content(processed_url, media_type, extension):
                        continue

                    # Create filename based on mode
                    safe_title = self.sanitize_filename(post_title)

                    if media_only_mode:
                        # Descriptive filename for media-only mode
                        media_type_prefix = media_type.upper()
                        if len(media_urls) > 1:
                            filename = f"[{media_type_prefix}]_{safe_title}_{i+1}_{post_id}{extension}"
                        else:
                            filename = f"[{media_type_prefix}]_{safe_title}_{post_id}{extension}"
                    else:
                        # Standard filename for bundled mode
                        if len(media_urls) > 1:
                            filename = f"{safe_title}_{i+1}{extension}"
                        else:
                            filename = f"{safe_title}{extension}"

                    filepath = media_dir / filename

                    # Avoid overwriting existing files
                    counter = 1
                    original_filepath = filepath
                    while filepath.exists():
                        name_part = original_filepath.stem
                        ext_part = original_filepath.suffix
                        filepath = media_dir / f"{name_part}_{counter}{ext_part}"
                        counter += 1

                    # Download file
                    if self.download_file(processed_url, filepath):
                        # Detect quality and validate content
                        quality_info = self.detect_media_quality(processed_url, filepath)

                        # Check if content is a placeholder
                        if self.is_placeholder_content(processed_url, filepath):
                            self.logger.warning(f"Placeholder content detected, skipping: {filepath}")
                            filepath.unlink(missing_ok=True)
                            continue

                        downloaded_files[url] = {
                            'local_path': str(filepath.relative_to(self.base_download_dir)),
                            'media_type': media_type,
                            'file_size': filepath.stat().st_size,
                            'extension': extension,
                            'quality_info': quality_info
                        }

                        # Validate media files
                        if not self.validate_media_file(filepath, media_type):
                            self.logger.warning(f"Invalid {media_type} file: {filepath}")
                            filepath.unlink(missing_ok=True)
                            del downloaded_files[url]
                        # Check file constraints (size, resolution, quality)
                        elif not self.check_file_constraints(filepath, quality_info):
                            self.logger.info(f"File doesn't meet constraints, removing: {filepath}")
                            filepath.unlink(missing_ok=True)
                            del downloaded_files[url]
                        else:
                            quality_desc = f"{quality_info['estimated_quality']} quality ({quality_info['resolution']})"
                            self.logger.info(f"Successfully downloaded {media_type} [{quality_desc}]: {filepath.name}")

            except Exception as e:
                self.logger.error(f"Error processing media URL {url}: {e}")
                continue
                
        return downloaded_files
        
    def is_downloadable_url(self, url: str) -> bool:
        """Check if URL points to a downloadable media file, filtering out thumbnails"""
        # Skip thumbnail URLs
        thumbnail_patterns = [
            'thumbs.redd.it', 'preview.redd.it', 'external-preview.redd.it',
            'b.thumbs.redditmedia.com', 'a.thumbs.redditmedia.com',
            '/thumb/', '/thumbnail/', '_thumb.', '_thumbnail.',
            's.redd.it', 'styles.redditmedia.com'
        ]

        if any(pattern in url.lower() for pattern in thumbnail_patterns):
            return False

        downloadable_domains = [
            'i.redd.it', 'i.imgur.com', 'imgur.com', 'v.redd.it',
            'gfycat.com', 'redgifs.com', 'giant.gfycat.com', 'thumbs2.redgifs.com'
        ]

        downloadable_extensions = [
            '.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff',
            '.mp4', '.webm', '.mov', '.avi', '.mkv', '.flv', '.wmv', '.m4v'
        ]

        # Skip external video platforms that require special handling
        skip_domains = ['youtube.com', 'youtu.be', 'twitter.com', 'tiktok.com']
        for domain in skip_domains:
            if domain in url:
                return False

        # Check domain
        for domain in downloadable_domains:
            if domain in url:
                return True

        # Check extension
        for ext in downloadable_extensions:
            if url.lower().endswith(ext):
                return True

        return False

    def should_download_content(self, url: str, media_type: str, file_extension: str) -> bool:
        """Check if content should be downloaded based on configuration filters"""
        # Check content type filters
        if self.include_types and media_type not in self.include_types:
            self.logger.debug(f"Skipping {media_type} - not in include_types: {url}")
            return False

        if self.exclude_types and media_type in self.exclude_types:
            self.logger.debug(f"Skipping {media_type} - in exclude_types: {url}")
            return False

        # Check format filters
        if media_type in ['image', 'gif'] and file_extension.lower() not in self.image_formats:
            self.logger.debug(f"Skipping {file_extension} image - not in allowed formats: {url}")
            return False

        if media_type == 'video' and file_extension.lower() not in self.video_formats:
            self.logger.debug(f"Skipping {file_extension} video - not in allowed formats: {url}")
            return False

        # Check if it's a placeholder (if skip_placeholders is enabled)
        if self.skip_placeholders and self.is_placeholder_content(url):
            self.logger.debug(f"Skipping placeholder content: {url}")
            return False

        return True

    def check_file_constraints(self, filepath: Path, quality_info: Dict[str, Any]) -> bool:
        """Check if downloaded file meets size and quality constraints"""
        try:
            file_size = filepath.stat().st_size

            # Check file size constraints
            if file_size < self.min_file_size:
                self.logger.debug(f"File too small ({file_size} bytes): {filepath}")
                return False

            if self.max_file_size and file_size > self.max_file_size:
                self.logger.debug(f"File too large ({file_size} bytes): {filepath}")
                return False

            # Check resolution constraints
            if self.min_resolution:
                try:
                    if any(ext in str(filepath) for ext in ['.jpg', '.png', '.gif', '.webp']):
                        with Image.open(filepath) as img:
                            width, height = img.size
                            min_width, min_height = self.min_resolution
                            if width < min_width or height < min_height:
                                self.logger.debug(f"Resolution too low ({width}x{height}): {filepath}")
                                return False
                except Exception as e:
                    self.logger.debug(f"Could not check resolution: {e}")

            # Check quality filter
            if self.quality_filter:
                estimated_quality = quality_info.get('estimated_quality', 'medium')
                quality_levels = ['low', 'medium', 'high', 'very_high']

                if quality_levels.index(estimated_quality) < quality_levels.index(self.quality_filter):
                    self.logger.debug(f"Quality too low ({estimated_quality}): {filepath}")
                    return False

            return True

        except Exception as e:
            self.logger.debug(f"Error checking file constraints: {e}")
            return False

    def prioritize_media_urls(self, urls: List[str]) -> List[str]:
        """Prioritize media URLs by quality and source, eliminating thumbnails"""
        if not urls:
            return urls

        # Filter out thumbnails and low-quality sources
        filtered_urls = [url for url in urls if self.is_downloadable_url(url)]

        # Priority scoring: higher score = better quality/source
        def get_url_priority(url: str) -> int:
            score = 0

            # Prefer original sources over previews
            if 'i.redd.it' in url:
                score += 100
            elif 'i.imgur.com' in url:
                score += 90
            elif 'v.redd.it' in url:
                score += 85
            elif 'giant.gfycat.com' in url:
                score += 80
            elif 'thumbs2.redgifs.com' in url:
                score += 75
            elif 'imgur.com' in url:
                score += 70
            elif 'gfycat.com' in url:
                score += 60
            elif 'redgifs.com' in url:
                score += 55

            # Prefer higher quality video formats
            if 'DASH_720' in url:
                score += 20
            elif 'DASH_480' in url:
                score += 15
            elif 'DASH_360' in url:
                score += 10
            elif 'DASH_240' in url:
                score += 5

            # Prefer certain file formats
            if url.endswith('.mp4'):
                score += 10
            elif url.endswith('.webm'):
                score += 8
            elif url.endswith('.gif'):
                score += 6
            elif url.endswith('.png'):
                score += 5
            elif url.endswith('.jpg'):
                score += 3

            return score

        # Sort by priority (highest first) and remove duplicates
        prioritized = sorted(set(filtered_urls), key=get_url_priority, reverse=True)
        return prioritized

    def validate_media_file(self, filepath: Path, media_type: str) -> bool:
        """Enhanced validation that checks file integrity and quality"""
        try:
            # Check file size (must be > 0)
            if filepath.stat().st_size == 0:
                self.logger.warning(f"File is empty: {filepath}")
                return False

            # Check for minimum file sizes to avoid placeholder images
            min_sizes = {
                'image': 1024,  # 1KB minimum for images
                'gif': 2048,    # 2KB minimum for GIFs
                'video': 10240  # 10KB minimum for videos
            }

            min_size = min_sizes.get(media_type, 512)
            if filepath.stat().st_size < min_size:
                self.logger.warning(f"File too small ({filepath.stat().st_size} bytes): {filepath}")
                return False

            if media_type in ['image', 'gif']:
                # Enhanced image/gif validation
                with Image.open(filepath) as img:
                    img.verify()

                # Re-open for dimension check (verify() closes the image)
                with Image.open(filepath) as img:
                    width, height = img.size

                    # Check for minimum dimensions to avoid tiny placeholder images
                    if width < 50 or height < 50:
                        self.logger.warning(f"Image too small ({width}x{height}): {filepath}")
                        return False

                    # Check for suspicious aspect ratios (likely placeholders)
                    aspect_ratio = width / height
                    if aspect_ratio > 10 or aspect_ratio < 0.1:
                        self.logger.warning(f"Suspicious aspect ratio ({aspect_ratio:.2f}): {filepath}")
                        return False

                    # For GIFs, check if it's actually animated
                    if media_type == 'gif' and hasattr(img, 'is_animated'):
                        if not img.is_animated:
                            self.logger.info(f"Static GIF detected: {filepath}")

                return True

            elif media_type == 'video':
                # Enhanced video validation
                with open(filepath, 'rb') as f:
                    header = f.read(32)  # Read more bytes for better detection

                    # Check for common video file signatures
                    video_signatures = [
                        b'\x00\x00\x00\x18ftypmp4',  # MP4
                        b'\x00\x00\x00\x20ftypmp4',  # MP4 variant
                        b'\x00\x00\x00\x1cftypisom', # MP4 ISO
                        b'\x1a\x45\xdf\xa3',         # WebM/MKV
                        b'RIFF',                      # AVI (starts with RIFF)
                        b'ftyp',                      # Generic MP4 family
                    ]

                    is_valid_video = False
                    for sig in video_signatures:
                        if sig in header:
                            is_valid_video = True
                            break

                    if not is_valid_video:
                        self.logger.warning(f"Invalid video signature: {filepath}")
                        return False

                    # Additional check: ensure file is not just a header
                    if filepath.stat().st_size < 50000:  # 50KB minimum for meaningful video
                        self.logger.warning(f"Video file too small: {filepath}")
                        return False

                return True
            else:
                # For unknown types, just check file size
                return filepath.stat().st_size > 100

        except Exception as e:
            self.logger.warning(f"Media validation failed for {filepath}: {e}")
            return False

    def detect_media_quality(self, url: str, filepath: Optional[Path] = None) -> Dict[str, Any]:
        """Detect media quality information from URL or file"""
        quality_info = {
            'resolution': 'unknown',
            'quality_score': 0,
            'source_type': 'unknown',
            'estimated_quality': 'medium'
        }

        try:
            # Analyze URL for quality indicators
            if 'DASH_720' in url or '720p' in url:
                quality_info['resolution'] = '720p'
                quality_info['quality_score'] = 80
                quality_info['estimated_quality'] = 'high'
            elif 'DASH_480' in url or '480p' in url:
                quality_info['resolution'] = '480p'
                quality_info['quality_score'] = 60
                quality_info['estimated_quality'] = 'medium'
            elif 'DASH_360' in url or '360p' in url:
                quality_info['resolution'] = '360p'
                quality_info['quality_score'] = 40
                quality_info['estimated_quality'] = 'low'
            elif 'DASH_240' in url or '240p' in url:
                quality_info['resolution'] = '240p'
                quality_info['quality_score'] = 20
                quality_info['estimated_quality'] = 'low'

            # Determine source type
            if 'i.redd.it' in url:
                quality_info['source_type'] = 'reddit_direct'
                quality_info['quality_score'] += 20
            elif 'i.imgur.com' in url:
                quality_info['source_type'] = 'imgur_direct'
                quality_info['quality_score'] += 15
            elif 'v.redd.it' in url:
                quality_info['source_type'] = 'reddit_video'
                quality_info['quality_score'] += 10
            elif 'giant.gfycat.com' in url:
                quality_info['source_type'] = 'gfycat_direct'
                quality_info['quality_score'] += 10
            elif 'thumbs2.redgifs.com' in url:
                quality_info['source_type'] = 'redgifs_direct'
                quality_info['quality_score'] += 8

            # If file exists, get actual dimensions
            if filepath and filepath.exists():
                try:
                    if any(ext in str(filepath) for ext in ['.jpg', '.png', '.gif', '.webp']):
                        with Image.open(filepath) as img:
                            width, height = img.size
                            quality_info['resolution'] = f"{width}x{height}"

                            # Score based on resolution
                            pixel_count = width * height
                            if pixel_count >= 1920 * 1080:  # 1080p+
                                quality_info['quality_score'] += 30
                                quality_info['estimated_quality'] = 'very_high'
                            elif pixel_count >= 1280 * 720:  # 720p+
                                quality_info['quality_score'] += 20
                                quality_info['estimated_quality'] = 'high'
                            elif pixel_count >= 640 * 480:   # 480p+
                                quality_info['quality_score'] += 10
                                quality_info['estimated_quality'] = 'medium'
                            else:
                                quality_info['estimated_quality'] = 'low'

                except Exception as e:
                    self.logger.debug(f"Could not analyze file dimensions: {e}")

        except Exception as e:
            self.logger.debug(f"Error detecting media quality: {e}")

        return quality_info

    def is_placeholder_content(self, url: str, filepath: Optional[Path] = None) -> bool:
        """Detect if content is likely a placeholder or thumbnail"""
        # Check URL patterns that indicate placeholders
        placeholder_patterns = [
            'placeholder', 'thumb', 'preview', 'default', 'loading',
            'spinner', 'blank', 'empty', 'missing', 'error'
        ]

        if any(pattern in url.lower() for pattern in placeholder_patterns):
            return True

        # Check file if available
        if filepath and filepath.exists():
            try:
                file_size = filepath.stat().st_size

                # Very small files are likely placeholders
                if file_size < 500:  # 500 bytes
                    return True

                # Check image dimensions if it's an image
                if any(ext in str(filepath) for ext in ['.jpg', '.png', '.gif', '.webp']):
                    with Image.open(filepath) as img:
                        width, height = img.size

                        # Common placeholder dimensions
                        placeholder_sizes = [
                            (1, 1), (10, 10), (16, 16), (32, 32), (50, 50),
                            (100, 100), (140, 140), (150, 150), (200, 200)
                        ]

                        if (width, height) in placeholder_sizes:
                            return True

                        # Very small images are likely placeholders
                        if width < 100 and height < 100:
                            return True

            except Exception:
                pass

        return False

    def get_download_stats(self, subreddit: str) -> Dict[str, int]:
        """Get download statistics for a subreddit with media type breakdown"""
        subreddit_dir = self.base_download_dir / subreddit / "media"

        if not subreddit_dir.exists():
            return {
                "total_files": 0,
                "total_size": 0,
                "post_count": 0,
                "images": 0,
                "gifs": 0,
                "videos": 0,
                "other": 0
            }

        total_files = 0
        total_size = 0
        post_count = len([d for d in subreddit_dir.iterdir() if d.is_dir()])

        # Count by media type
        images = 0
        gifs = 0
        videos = 0
        other = 0

        for post_dir in subreddit_dir.iterdir():
            if post_dir.is_dir():
                for file_path in post_dir.iterdir():
                    if file_path.is_file():
                        total_files += 1
                        total_size += file_path.stat().st_size

                        # Categorize by extension
                        ext = file_path.suffix.lower()
                        if ext in ['.jpg', '.jpeg', '.png', '.webp', '.bmp', '.tiff']:
                            images += 1
                        elif ext in ['.gif']:
                            gifs += 1
                        elif ext in ['.mp4', '.webm', '.mov', '.avi', '.mkv', '.flv', '.wmv', '.m4v']:
                            videos += 1
                        else:
                            other += 1

        return {
            "total_files": total_files,
            "total_size": total_size,
            "post_count": post_count,
            "images": images,
            "gifs": gifs,
            "videos": videos,
            "other": other
        }
