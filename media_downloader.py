#!/usr/bin/env python3
"""
Media Downloader - Module for downloading and organizing Reddit media files
"""

import os
import requests
import mimetypes
from pathlib import Path
from urllib.parse import urlparse, unquote
from typing import Dict, List, Optional, Tuple
import logging
from PIL import Image
import time


class MediaDownloader:
    """<PERSON>les downloading and organizing media files from Reddit posts"""
    
    def __init__(self, base_download_dir: str = "downloads", rate_limit_delay: float = 1.0):
        """
        Initialize the media downloader
        
        Args:
            base_download_dir: Base directory for downloads
            rate_limit_delay: Delay between downloads in seconds
        """
        self.base_download_dir = Path(base_download_dir)
        self.rate_limit_delay = rate_limit_delay
        self.session = requests.Session()
        
        # Set user agent
        self.session.headers.update({
            'User-Agent': 'RedditScraper/1.0 (Educational/Research Purpose)',
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
        
        self.logger = logging.getLogger(__name__)
        
        # Create base download directory
        self.base_download_dir.mkdir(exist_ok=True)
        
    def sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for Windows compatibility"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Remove or replace other problematic characters
        filename = filename.replace(' ', '_')
        filename = filename.strip('. ')
        
        # Limit length
        if len(filename) > 200:
            name, ext = os.path.splitext(filename)
            filename = name[:200-len(ext)] + ext
            
        return filename if filename else 'unnamed'
        
    def get_file_extension(self, url: str, content_type: str = None) -> str:
        """Determine file extension from URL or content type"""
        # Try to get extension from URL
        parsed_url = urlparse(url)
        path = unquote(parsed_url.path)
        _, ext = os.path.splitext(path)
        
        if ext and ext.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.mp4', '.webm', '.mov']:
            return ext.lower()
            
        # Try to get extension from content type
        if content_type:
            ext = mimetypes.guess_extension(content_type.split(';')[0])
            if ext:
                return ext.lower()
                
        # Default extensions based on common Reddit media
        if 'i.redd.it' in url:
            return '.jpg'
        elif 'v.redd.it' in url:
            return '.mp4'
        elif 'imgur.com' in url:
            return '.jpg'
            
        return '.unknown'
        
    def create_media_directory(self, subreddit: str, post_id: str) -> Path:
        """Create organized directory structure for media files"""
        media_dir = self.base_download_dir / subreddit / "media" / post_id
        media_dir.mkdir(parents=True, exist_ok=True)
        return media_dir
        
    def download_file(self, url: str, filepath: Path, max_retries: int = 3) -> bool:
        """
        Download a file from URL to filepath
        
        Args:
            url: URL to download from
            filepath: Local filepath to save to
            max_retries: Maximum number of retry attempts
            
        Returns:
            True if successful, False otherwise
        """
        for attempt in range(max_retries):
            try:
                time.sleep(self.rate_limit_delay)
                
                response = self.session.get(url, stream=True, timeout=30)
                response.raise_for_status()
                
                # Check file size (limit to 100MB)
                content_length = response.headers.get('content-length')
                if content_length and int(content_length) > 100 * 1024 * 1024:
                    self.logger.warning(f"File too large ({content_length} bytes): {url}")
                    return False
                
                # Download file
                with open(filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            
                self.logger.info(f"Downloaded: {filepath.name}")
                return True
                
            except requests.exceptions.RequestException as e:
                self.logger.warning(f"Download failed (attempt {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    self.logger.error(f"Failed to download {url} after {max_retries} attempts")
                    return False
                time.sleep(2 ** attempt)  # Exponential backoff
                
            except Exception as e:
                self.logger.error(f"Unexpected error downloading {url}: {e}")
                return False
                
        return False
        
    def process_imgur_url(self, url: str) -> str:
        """Process Imgur URLs to get direct image links"""
        if 'imgur.com' in url and not url.endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
            # Convert imgur page URL to direct image URL
            if '/a/' in url or '/gallery/' in url:
                # Album or gallery - return as is for now
                return url
            else:
                # Single image - add .jpg extension
                if url.endswith('/'):
                    url = url[:-1]
                return url + '.jpg'
        return url
        
    def download_media_files(self, post_data: dict, subreddit: str) -> Dict[str, str]:
        """
        Download all media files for a post
        
        Args:
            post_data: Dictionary containing post information
            subreddit: Subreddit name
            
        Returns:
            Dictionary mapping original URLs to local file paths
        """
        media_urls = post_data.get('media_urls', [])
        post_id = post_data.get('post_id', 'unknown')
        post_title = post_data.get('title', 'untitled')
        
        if not media_urls:
            return {}
            
        # Create media directory
        media_dir = self.create_media_directory(subreddit, post_id)
        
        downloaded_files = {}
        
        for i, url in enumerate(media_urls):
            try:
                # Process special URLs
                processed_url = self.process_imgur_url(url)
                
                # Skip if URL is not downloadable
                if not self.is_downloadable_url(processed_url):
                    self.logger.info(f"Skipping non-downloadable URL: {processed_url}")
                    continue
                
                # Get file extension
                response = self.session.head(processed_url, timeout=10)
                content_type = response.headers.get('content-type', '')
                extension = self.get_file_extension(processed_url, content_type)
                
                # Create filename
                safe_title = self.sanitize_filename(post_title)
                if len(media_urls) > 1:
                    filename = f"{safe_title}_{i+1}{extension}"
                else:
                    filename = f"{safe_title}{extension}"
                    
                filepath = media_dir / filename
                
                # Avoid overwriting existing files
                counter = 1
                original_filepath = filepath
                while filepath.exists():
                    name_part = original_filepath.stem
                    ext_part = original_filepath.suffix
                    filepath = media_dir / f"{name_part}_{counter}{ext_part}"
                    counter += 1
                
                # Download file
                if self.download_file(processed_url, filepath):
                    downloaded_files[url] = str(filepath.relative_to(self.base_download_dir))
                    
                    # Validate image files
                    if extension.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
                        if not self.validate_image(filepath):
                            self.logger.warning(f"Invalid image file: {filepath}")
                            filepath.unlink(missing_ok=True)
                            del downloaded_files[url]
                            
            except Exception as e:
                self.logger.error(f"Error processing media URL {url}: {e}")
                continue
                
        return downloaded_files
        
    def is_downloadable_url(self, url: str) -> bool:
        """Check if URL points to a downloadable media file"""
        downloadable_domains = [
            'i.redd.it', 'i.imgur.com', 'imgur.com', 'v.redd.it',
            'gfycat.com', 'redgifs.com'
        ]
        
        downloadable_extensions = [
            '.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp',
            '.mp4', '.webm', '.mov', '.avi', '.mkv'
        ]
        
        # Check domain
        for domain in downloadable_domains:
            if domain in url:
                return True
                
        # Check extension
        for ext in downloadable_extensions:
            if url.lower().endswith(ext):
                return True
                
        return False
        
    def validate_image(self, filepath: Path) -> bool:
        """Validate that an image file is not corrupted"""
        try:
            with Image.open(filepath) as img:
                img.verify()
            return True
        except Exception:
            return False
            
    def get_download_stats(self, subreddit: str) -> Dict[str, int]:
        """Get download statistics for a subreddit"""
        subreddit_dir = self.base_download_dir / subreddit / "media"
        
        if not subreddit_dir.exists():
            return {"total_files": 0, "total_size": 0, "post_count": 0}
            
        total_files = 0
        total_size = 0
        post_count = len(list(subreddit_dir.iterdir()))
        
        for post_dir in subreddit_dir.iterdir():
            if post_dir.is_dir():
                for file_path in post_dir.iterdir():
                    if file_path.is_file():
                        total_files += 1
                        total_size += file_path.stat().st_size
                        
        return {
            "total_files": total_files,
            "total_size": total_size,
            "post_count": post_count
        }
