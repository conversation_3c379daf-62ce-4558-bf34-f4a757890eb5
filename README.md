# Reddit Scraper

A comprehensive, production-ready Reddit scraping application that extracts posts and media from subreddits with advanced filtering, quality detection, and external source support. Fully tested and optimized for Windows 11.

## 🚨 Important Notice

**Reddit API Changes (2023)**: Reddit deprecated free API access in July 2023. This scraper uses web scraping techniques as an alternative, designed to be respectful and compliant with Reddit's terms of service.

**Limitations**:
- Reddit limits viewing to approximately 1000 posts per subreddit
- Rate limiting is enforced to be respectful to Reddit's servers
- Some subreddits may be private or restricted

## 🆕 Recent Updates (v2.0)

- ✅ **Fixed Critical Bugs**: Resolved GIF download filtering, download statistics, and duplicate file issues
- ✅ **Enhanced External Sources**: Improved Redgifs and Gfycat URL processing with direct link extraction
- ✅ **Unified Post Bundling**: Each post gets its own folder with media and metadata
- ✅ **Media-Only Mode**: Flat directory structure with descriptive filenames
- ✅ **Advanced Quality Detection**: Automatic resolution detection and quality scoring
- ✅ **Comprehensive Testing**: 21 unit tests + end-to-end validation ensuring reliability

## ✨ Features

### Data Collection
- ✅ Scrape posts from any public subreddit
- ✅ Extract comprehensive metadata (title, author, score, comments, timestamps, etc.)
- ✅ Support for different post types (text, image, video, link, gallery)
- ✅ Handle pagination up to Reddit's limits
- ✅ Respectful rate limiting and error handling

### Media Download
- ✅ Download images, videos, and GIFs with advanced filtering
- ✅ Support for Reddit-hosted media (i.redd.it, v.redd.it)
- ✅ Support for external sources (Imgur, Gfycat, Redgifs)
- ✅ **Enhanced Redgifs Processing**: Direct MP4 link extraction via thumbs2.redgifs.com
- ✅ **Imgur Gallery Support**: Automatic .gifv to .mp4 conversion
- ✅ Intelligent URL prioritization and quality detection
- ✅ Placeholder content filtering and quality validation
- ✅ **Duplicate Prevention**: Smart duplicate detection prevents re-downloading
- ✅ Configurable file type, size, and resolution filters
- ✅ **Unified Post Bundling**: Each post gets dedicated folder with media + metadata
- ✅ **Media-Only Mode**: Flat structure with descriptive filenames like `[GIF]_title_postid.gif`
- ✅ Automatic file naming and conflict resolution

### Data Storage
- ✅ Export to JSON and CSV formats
- ✅ Structured directory organization
- ✅ Media-to-post mapping
- ✅ Comprehensive summary reports
- ✅ Data integrity validation

### Technical Features
- ✅ **Windows 11 Optimized**: Fully tested and compatible
- ✅ **Production Ready**: 21 unit tests + comprehensive end-to-end testing
- ✅ Virtual environment support with dependency isolation
- ✅ Comprehensive logging with configurable levels and file output
- ✅ Advanced configuration system (CLI + JSON config files)
- ✅ **Enhanced Error Handling**: Robust retry logic and graceful failure recovery
- ✅ **Smart Duplicate Detection**: Prevents downloading same content multiple times
- ✅ Rich CLI interface with 25+ configuration options
- ✅ **Multiple Download Modes**: Media-only, bundled, and legacy structures
- ✅ **Quality Assurance**: File validation, integrity checks, and placeholder filtering

## 🛠️ Installation

### Prerequisites
- Python 3.8 or higher
- Windows 11 (tested and optimized)

### Setup

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd reddit-scraper
   ```

2. **Create and activate virtual environment**
   ```bash
   python -m venv reddit_scraper_env
   .\reddit_scraper_env\Scripts\Activate.ps1
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

## 🚀 Usage

### Basic Usage

```bash
# Scrape 50 posts from r/python
python main.py python --limit 50

# Scrape with media download
python main.py MachineLearning --limit 100 --download-media

# Export to both JSON and CSV
python main.py AskReddit --limit 200 --format both

# Scrape top posts
python main.py programming --sort top --limit 100
```

### Command Line Options

```bash
python main.py <subreddit> [options]

Required:
  subreddit              Subreddit name (without r/ prefix)

Core Options:
  --limit, -l           Maximum posts to scrape (default: 100, max: ~1000)
  --sort, -s            Sort method: hot, new, top, rising (default: hot)
  --download-media, -m  Download media files
  --no-media            Skip media download
  --format, -f          Output format: json, csv, both (default: json)
  --output-dir, -o      Output directory (default: downloads)

Download Modes:
  --media-only          Media-only mode: flat structure with descriptive names
  --bundled-posts       Bundled mode: each post gets its own folder (default)

Content Filtering:
  --include-types       Include specific types: image, gif, video, text, link
  --exclude-types       Exclude specific types
  --image-formats       Allowed image formats: jpg, png, gif, webp
  --video-formats       Allowed video formats: mp4, webm, mov

Quality Control:
  --min-file-size       Minimum file size in bytes (default: 2048)
  --max-file-size       Maximum file size in bytes (default: 100MB)
  --min-resolution      Minimum resolution (e.g., 1920x1080)
  --quality-filter      Quality level: low, medium, high, very_high
  --skip-placeholders   Skip placeholder and thumbnail images

System Options:
  --rate-limit          Delay between requests in seconds (default: 2.0)
  --max-retries         Maximum download retries (default: 3)
  --timeout             Request timeout in seconds (default: 30)
  --log-level           Logging level: DEBUG, INFO, WARNING, ERROR
  --log-file            Log file path
  --config-file         JSON configuration file path
  --dry-run             Show what would be done without scraping
```

### Examples

```bash
# Basic scraping
python main.py python --limit 50 --sort hot

# Download media with custom output directory
python main.py EarthPorn --download-media --output-dir ./my_downloads --limit 25

# Media-only mode with GIFs and videos only
python main.py gifs --media-only --include-types gif video --limit 50

# High-quality images only
python main.py EarthPorn --download-media --quality-filter high --min-resolution 1920x1080

# Bundled posts with comprehensive data
python main.py MachineLearning --bundled-posts --format both --download-media

# High-volume scraping with detailed logging
python main.py MachineLearning --limit 500 --format both --log-level DEBUG --log-file scraper.log

# External source testing (Redgifs, Imgur)
python main.py HighQualityGifs --download-media --include-types gif video --log-level DEBUG

# Quick test run
python main.py test --limit 5 --dry-run
```

## 📁 Output Structure

### Bundled Mode (Default)
```
downloads/
├── <subreddit>/
│   ├── data/
│   │   ├── posts_YYYYMMDD_HHMMSS.json
│   │   ├── posts_YYYYMMDD_HHMMSS.csv
│   │   ├── media_mapping_YYYYMMDD_HHMMSS.json
│   │   └── summary_report_YYYYMMDD_HHMMSS.txt
│   └── posts/
│       ├── <post_id_1>/
│       │   ├── post_title_1.jpg
│       │   ├── post_title_2.mp4
│       │   └── metadata.json
│       └── <post_id_2>/
│           ├── post_title.gif
│           └── metadata.json
└── reddit_scraper.log
```

### Media-Only Mode
```
downloads/
├── <subreddit>/
│   └── media_only/
│       ├── [GIF]_Amazing_Animation_postid123.gif
│       ├── [VIDEO]_Cool_Video_postid456.mp4
│       └── [IMAGE]_Beautiful_Photo_postid789.jpg
└── reddit_scraper.log
```

## 📊 Data Format

### JSON Output
```json
{
  "metadata": {
    "subreddit": "python",
    "scraped_at": "2024-01-01T12:00:00Z",
    "total_posts": 100,
    "scraper_version": "1.0"
  },
  "posts": [
    {
      "title": "Post Title",
      "author": "username",
      "score": 1234,
      "comments_count": 56,
      "created_utc": "2024-01-01T10:00:00Z",
      "post_type": "text",
      "url": "https://reddit.com/...",
      "permalink": "https://reddit.com/...",
      "content": "Post content...",
      "media_urls": ["https://i.redd.it/image.jpg"],
      "subreddit": "python",
      "post_id": "abc123",
      "upvote_ratio": 0.95,
      "flair": "Discussion"
    }
  ]
}
```

### CSV Output
All post data in tabular format with media URLs as semicolon-separated values.

## 🧪 Testing

The scraper includes a comprehensive test suite with 21 unit tests covering all functionality:

```bash
# Run all unit tests (21 tests)
python test_scraper.py

# Run specific test categories
python -m unittest test_scraper.TestRedditScraper.test_media_download
python -m unittest test_scraper.TestRedditScraper.test_external_url_processing

# End-to-end testing with real subreddits
python main.py gifs --limit 5 --download-media --log-level DEBUG

# Test external source processing
python main.py HighQualityGifs --limit 5 --download-media --log-level DEBUG

# Test media-only mode
python main.py gifs --limit 3 --media-only --include-types gif
```

### Test Coverage
- ✅ **URL Processing**: Reddit, Imgur, Gfycat, Redgifs URL handling
- ✅ **Media Download**: File download, validation, and quality detection
- ✅ **Data Storage**: JSON/CSV export, directory creation, file organization
- ✅ **Content Filtering**: Type filtering, format validation, quality control
- ✅ **Error Handling**: Network errors, invalid URLs, file system issues
- ✅ **Configuration**: CLI arguments, config files, environment variables

## 🎛️ Advanced Configuration

### Configuration File Support

Create a JSON configuration file to set default options:

```json
{
  "limit": 50,
  "sort": "hot",
  "download_media": true,
  "media_only": false,
  "bundled_posts": true,
  "format": "json",
  "output_dir": "downloads",
  "rate_limit": 2.0,
  "log_level": "INFO",

  "include_types": ["image", "gif", "video"],
  "exclude_types": [],
  "image_formats": ["jpg", "jpeg", "png", "gif", "webp"],
  "video_formats": ["mp4", "webm", "mov"],

  "min_file_size": 2048,
  "max_file_size": 104857600,
  "min_resolution": "640x480",
  "skip_placeholders": true,
  "quality_filter": "medium",

  "max_retries": 3,
  "timeout": 30,
  "user_agent": "RedditScraper/1.0"
}
```

Use with: `python main.py r/subreddit --config-file config.json`

### Advanced CLI Options

#### Content Filtering
```bash
# Include only specific content types
python main.py r/earthporn --include-types image gif

# Exclude specific content types
python main.py r/funny --exclude-types video

# Filter by file formats
python main.py r/gifs --image-formats jpg png --video-formats mp4

# File size constraints
python main.py r/pics --min-file-size 5000 --max-file-size 10485760

# Resolution filtering
python main.py r/wallpapers --min-resolution 1920x1080
```

#### Quality Control
```bash
# Quality filtering (low, medium, high, very_high)
python main.py r/earthporn --quality-filter high

# Skip placeholder images
python main.py r/pics --skip-placeholders

# Advanced retry and timeout settings
python main.py r/videos --max-retries 5 --timeout 60
```

#### Download Modes
```bash
# Media-only mode (flat structure with descriptive names)
python main.py earthporn --media-only --include-types image

# Bundled mode (each post gets its own folder with metadata)
python main.py funny --bundled-posts --download-media

# Legacy mode (for compatibility)
python main.py pics --download-media --output-dir legacy_downloads
```

### Quality Detection System

The scraper includes advanced quality detection and scoring:

- **Resolution Analysis**: Extracts dimensions from URLs and files automatically
- **Source Prioritization**: Prefers original sources (i.redd.it) over previews/thumbnails
- **Format Scoring**: Prioritizes high-quality formats (MP4 > WEBM, PNG > JPG)
- **Placeholder Detection**: Filters out loading images, thumbnails, and low-quality content
- **External Source Processing**:
  - **Redgifs**: Direct MP4 extraction via thumbs2.redgifs.com
  - **Imgur**: Automatic .gifv to .mp4 conversion
  - **Gfycat**: Direct video link processing
- **Quality Levels**:
  - `very_high`: 1080p+ or premium sources (1920x1080+)
  - `high`: 720p+ or good sources (1280x720+)
  - `medium`: 480p+ or standard sources (640x480+)
  - `low`: Below 480p or low-quality sources
- **File Validation**: Integrity checks, minimum size requirements, format verification

## ⚙️ Configuration

### Environment Variables
```bash
REDDIT_SCRAPER_RATE_LIMIT=2.0
REDDIT_SCRAPER_MAX_RETRIES=3
REDDIT_SCRAPER_TIMEOUT=30
REDDIT_SCRAPER_MAX_FILE_SIZE=100
REDDIT_SCRAPER_OUTPUT_DIR=downloads
REDDIT_SCRAPER_LOG_LEVEL=INFO
```

### Configuration File
Create `config.json` for custom settings:
```json
{
  "rate_limit_delay": 2.0,
  "max_retries": 3,
  "request_timeout": 30,
  "max_file_size_mb": 100,
  "default_output_dir": "downloads"
}
```

## 🚨 Limitations & Considerations

### Reddit Limitations
- **~1000 post limit**: Reddit restricts viewing to approximately 1000 posts per subreddit
- **Rate limiting**: Requests are rate-limited to be respectful
- **Private subreddits**: Cannot access private or restricted subreddits
- **Deleted content**: Some posts/comments may be deleted or removed

### Technical Limitations
- **Media size**: Files larger than 100MB are skipped by default
- **Video formats**: Some video formats may not be downloadable
- **Gallery posts**: Complex gallery posts may have limited support

### Legal & Ethical Considerations
- **Terms of Service**: Use responsibly and in compliance with Reddit's ToS
- **Rate limiting**: Built-in delays to avoid overwhelming Reddit's servers
- **Educational use**: Designed for research and educational purposes

## 🔧 Troubleshooting

### Common Issues

**"Subreddit does not exist"**
- Check spelling of subreddit name
- Ensure subreddit is public
- Try without the "r/" prefix

**"No posts scraped"**
- Subreddit might be empty or private
- Check network connection
- Try increasing rate limit delay

**"Media download failed"**
- Some media URLs may be expired or invalid
- Check available disk space
- Verify network connectivity
- Try with `--log-level DEBUG` for detailed error info

**"GIFs not downloading"**
- Ensure `--include-types gif` is set
- Check file format filters with `--image-formats gif`
- Verify minimum file size with `--min-file-size`

**"Duplicate files created"**
- This is normal behavior when re-running scraper
- Use fresh output directories for clean runs
- Files are skipped if they already exist (no re-download)

**"Rate limited"**
- Increase `--rate-limit` value (try 3.0 or higher)
- Wait before retrying
- Check if IP is temporarily blocked

**"External URLs not working (Redgifs/Imgur)"**
- Enable debug logging: `--log-level DEBUG`
- Check network connectivity to external sources
- Some URLs may require different processing methods

### Debug Mode
```bash
# Comprehensive debugging
python main.py <subreddit> --log-level DEBUG --log-file debug.log --limit 5

# Test specific functionality
python main.py gifs --limit 3 --download-media --log-level DEBUG --include-types gif

# Test external sources
python main.py HighQualityGifs --limit 5 --download-media --log-level DEBUG
```

### Performance Tips
- Use `--media-only` for faster downloads without post data
- Set appropriate `--min-file-size` to skip tiny files
- Use `--quality-filter` to get only high-quality content
- Enable `--skip-placeholders` to avoid downloading thumbnails

## 📝 License

This project is for educational and research purposes. Please respect Reddit's Terms of Service and use responsibly.

## 🤝 Contributing

1. **Test thoroughly**: Run the test suite and test with various subreddits
2. **Report issues**: Include detailed error logs and reproduction steps
3. **Suggest improvements**: Better Reddit compliance, new features, performance
4. **Add support**: Additional media formats, external sources, quality detection

## 📈 Changelog

### v2.0 (Latest)
- ✅ Fixed critical GIF download filtering bug
- ✅ Enhanced Redgifs URL processing with direct MP4 links
- ✅ Added unified post bundling with metadata
- ✅ Implemented media-only mode with descriptive filenames
- ✅ Fixed download statistics and duplicate file issues
- ✅ Added comprehensive test suite (21 unit tests)
- ✅ Improved external source handling (Imgur, Gfycat)
- ✅ Enhanced quality detection and validation

### v1.0
- Initial release with basic scraping functionality
- Reddit post extraction and media download
- JSON/CSV export capabilities
- Basic configuration options

## 📞 Support

For issues and questions:
1. **Check troubleshooting**: Review the comprehensive troubleshooting section
2. **Run tests**: Execute `python test_scraper.py` to validate setup
3. **Enable debug logging**: Use `--log-level DEBUG` for detailed error info
4. **Test with known subreddits**: Try `r/gifs` or `r/pics` for validation
5. **Check dependencies**: Ensure all requirements are installed correctly
