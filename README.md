# Reddit Scraper

A comprehensive Reddit scraping application that extracts posts and media from subreddits while respecting Reddit's current API limitations and terms of service.

## 🚨 Important Notice

**Reddit API Changes (2023)**: Reddit deprecated free API access in July 2023. This scraper uses web scraping techniques as an alternative, designed to be respectful and compliant with Reddit's terms of service.

**Limitations**: 
- Reddit limits viewing to approximately 1000 posts per subreddit
- Rate limiting is enforced to be respectful to Reddit's servers
- Some subreddits may be private or restricted

## ✨ Features

### Data Collection
- ✅ Scrape posts from any public subreddit
- ✅ Extract comprehensive metadata (title, author, score, comments, timestamps, etc.)
- ✅ Support for different post types (text, image, video, link, gallery)
- ✅ Handle pagination up to Reddit's limits
- ✅ Respectful rate limiting and error handling

### Media Download
- ✅ Download images, videos, and GIFs with advanced filtering
- ✅ Support for Reddit-hosted media (i.redd.it, v.redd.it)
- ✅ Support for external sources (Imgur, Gfycat, Redgifs)
- ✅ Intelligent URL prioritization and quality detection
- ✅ Placeholder content filtering and quality validation
- ✅ Configurable file type, size, and resolution filters
- ✅ Organized folder structure for media files
- ✅ Automatic file naming and conflict resolution

### Data Storage
- ✅ Export to JSON and CSV formats
- ✅ Structured directory organization
- ✅ Media-to-post mapping
- ✅ Comprehensive summary reports
- ✅ Data integrity validation

### Technical Features
- ✅ Windows 11 compatibility
- ✅ Virtual environment support
- ✅ Comprehensive logging with configurable levels
- ✅ Advanced configuration system (CLI + JSON config files)
- ✅ Extensive filtering and quality control options
- ✅ Error recovery and retry logic
- ✅ Rich CLI interface with 25+ configuration options
- ✅ Media-only and bundled download modes

## 🛠️ Installation

### Prerequisites
- Python 3.8 or higher
- Windows 11 (tested and optimized)

### Setup

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd reddit-scraper
   ```

2. **Create and activate virtual environment**
   ```bash
   python -m venv reddit_scraper_env
   .\reddit_scraper_env\Scripts\Activate.ps1
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

## 🚀 Usage

### Basic Usage

```bash
# Scrape 50 posts from r/python
python main.py python --limit 50

# Scrape with media download
python main.py MachineLearning --limit 100 --download-media

# Export to both JSON and CSV
python main.py AskReddit --limit 200 --format both

# Scrape top posts
python main.py programming --sort top --limit 100
```

### Command Line Options

```bash
python main.py <subreddit> [options]

Required:
  subreddit              Subreddit name (without r/ prefix)

Optional:
  --limit, -l           Maximum posts to scrape (default: 100, max: ~1000)
  --sort, -s            Sort method: hot, new, top, rising (default: hot)
  --download-media, -m  Download media files
  --no-media            Skip media download
  --format, -f          Output format: json, csv, both (default: json)
  --output-dir, -o      Output directory (default: downloads)
  --rate-limit          Delay between requests in seconds (default: 2.0)
  --log-level           Logging level: DEBUG, INFO, WARNING, ERROR
  --log-file            Log file path
  --dry-run             Show what would be done without scraping
```

### Examples

```bash
# Basic scraping
python main.py python --limit 50 --sort hot

# Download media with custom output directory
python main.py EarthPorn --download-media --output-dir ./my_downloads --limit 25

# High-volume scraping with detailed logging
python main.py MachineLearning --limit 500 --format both --log-level DEBUG --log-file scraper.log

# Quick test run
python main.py test --limit 5 --dry-run
```

## 📁 Output Structure

```
downloads/
├── <subreddit>/
│   ├── data/
│   │   ├── posts_YYYYMMDD_HHMMSS.json
│   │   ├── posts_YYYYMMDD_HHMMSS.csv
│   │   ├── media_mapping_YYYYMMDD_HHMMSS.json
│   │   └── summary_report_YYYYMMDD_HHMMSS.txt
│   ├── media/
│   │   ├── <post_id_1>/
│   │   │   ├── post_title_1.jpg
│   │   │   └── post_title_2.mp4
│   │   └── <post_id_2>/
│   │       └── post_title.gif
│   └── logs/
└── reddit_scraper.log
```

## 📊 Data Format

### JSON Output
```json
{
  "metadata": {
    "subreddit": "python",
    "scraped_at": "2024-01-01T12:00:00Z",
    "total_posts": 100,
    "scraper_version": "1.0"
  },
  "posts": [
    {
      "title": "Post Title",
      "author": "username",
      "score": 1234,
      "comments_count": 56,
      "created_utc": "2024-01-01T10:00:00Z",
      "post_type": "text",
      "url": "https://reddit.com/...",
      "permalink": "https://reddit.com/...",
      "content": "Post content...",
      "media_urls": ["https://i.redd.it/image.jpg"],
      "subreddit": "python",
      "post_id": "abc123",
      "upvote_ratio": 0.95,
      "flair": "Discussion"
    }
  ]
}
```

### CSV Output
All post data in tabular format with media URLs as semicolon-separated values.

## 🧪 Testing

Run the test suite to validate functionality:

```bash
# Run all tests
python test_scraper.py

# Run only unit tests
python -m unittest test_scraper.TestRedditScraper

# Test with a real subreddit (integration test)
python test_scraper.py
```

## 🎛️ Advanced Configuration

### Configuration File Support

Create a JSON configuration file to set default options:

```json
{
  "limit": 50,
  "sort": "hot",
  "download_media": true,
  "media_only": false,
  "bundled_posts": true,
  "format": "json",
  "output_dir": "downloads",
  "rate_limit": 2.0,
  "log_level": "INFO",

  "include_types": ["image", "gif", "video"],
  "exclude_types": [],
  "image_formats": ["jpg", "jpeg", "png", "gif", "webp"],
  "video_formats": ["mp4", "webm", "mov"],

  "min_file_size": 2048,
  "max_file_size": 104857600,
  "min_resolution": "640x480",
  "skip_placeholders": true,
  "quality_filter": "medium",

  "max_retries": 3,
  "timeout": 30,
  "user_agent": "RedditScraper/1.0"
}
```

Use with: `python main.py r/subreddit --config-file config.json`

### Advanced CLI Options

#### Content Filtering
```bash
# Include only specific content types
python main.py r/earthporn --include-types image gif

# Exclude specific content types
python main.py r/funny --exclude-types video

# Filter by file formats
python main.py r/gifs --image-formats jpg png --video-formats mp4

# File size constraints
python main.py r/pics --min-file-size 5000 --max-file-size 10485760

# Resolution filtering
python main.py r/wallpapers --min-resolution 1920x1080
```

#### Quality Control
```bash
# Quality filtering (low, medium, high, very_high)
python main.py r/earthporn --quality-filter high

# Skip placeholder images
python main.py r/pics --skip-placeholders

# Advanced retry and timeout settings
python main.py r/videos --max-retries 5 --timeout 60
```

#### Download Modes
```bash
# Media-only mode (no post data, organized by media type)
python main.py r/earthporn --media-only

# Bundled mode (each post gets its own folder)
python main.py r/funny --bundled-posts
```

### Quality Detection System

The scraper automatically detects and scores media quality:

- **Resolution Analysis**: Extracts dimensions from URLs and files
- **Source Prioritization**: Prefers original sources over previews/thumbnails
- **Format Scoring**: Prioritizes high-quality formats (MP4 > WEBM, PNG > JPG)
- **Placeholder Detection**: Filters out loading images and thumbnails
- **Quality Levels**:
  - `very_high`: 1080p+ or high-quality sources
  - `high`: 720p+ or good sources
  - `medium`: 480p+ or standard sources
  - `low`: Below 480p or low-quality sources

## ⚙️ Configuration

### Environment Variables
```bash
REDDIT_SCRAPER_RATE_LIMIT=2.0
REDDIT_SCRAPER_MAX_RETRIES=3
REDDIT_SCRAPER_TIMEOUT=30
REDDIT_SCRAPER_MAX_FILE_SIZE=100
REDDIT_SCRAPER_OUTPUT_DIR=downloads
REDDIT_SCRAPER_LOG_LEVEL=INFO
```

### Configuration File
Create `config.json` for custom settings:
```json
{
  "rate_limit_delay": 2.0,
  "max_retries": 3,
  "request_timeout": 30,
  "max_file_size_mb": 100,
  "default_output_dir": "downloads"
}
```

## 🚨 Limitations & Considerations

### Reddit Limitations
- **~1000 post limit**: Reddit restricts viewing to approximately 1000 posts per subreddit
- **Rate limiting**: Requests are rate-limited to be respectful
- **Private subreddits**: Cannot access private or restricted subreddits
- **Deleted content**: Some posts/comments may be deleted or removed

### Technical Limitations
- **Media size**: Files larger than 100MB are skipped by default
- **Video formats**: Some video formats may not be downloadable
- **Gallery posts**: Complex gallery posts may have limited support

### Legal & Ethical Considerations
- **Terms of Service**: Use responsibly and in compliance with Reddit's ToS
- **Rate limiting**: Built-in delays to avoid overwhelming Reddit's servers
- **Educational use**: Designed for research and educational purposes

## 🔧 Troubleshooting

### Common Issues

**"Subreddit does not exist"**
- Check spelling of subreddit name
- Ensure subreddit is public
- Try without the "r/" prefix

**"No posts scraped"**
- Subreddit might be empty or private
- Check network connection
- Try increasing rate limit delay

**"Media download failed"**
- Some media URLs may be expired or invalid
- Check available disk space
- Verify network connectivity

**"Rate limited"**
- Increase `--rate-limit` value
- Wait before retrying
- Check if IP is temporarily blocked

### Debug Mode
```bash
python main.py <subreddit> --log-level DEBUG --log-file debug.log
```

## 📝 License

This project is for educational and research purposes. Please respect Reddit's Terms of Service and use responsibly.

## 🤝 Contributing

1. Test thoroughly with various subreddits
2. Report issues with detailed error logs
3. Suggest improvements for better Reddit compliance
4. Add support for additional media formats

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review log files for error details
3. Test with different subreddits
4. Ensure all dependencies are installed correctly
